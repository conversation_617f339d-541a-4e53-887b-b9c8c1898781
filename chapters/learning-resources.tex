% 量化学习资源章节
% 基于 quant-wiki/docs/repo/quant_learn.md 内容编译

\section{概述}

本章收集了关于系统化交易（量化交易）策略的资源清单，包括论文、软件、书籍、文章，帮助大家寻找、开发并运行此类策略。这些资源由LLMQuant社区整理，涵盖了量化交易的各个方面。

\subsection{资源概览}

在这里你能找到：
\begin{itemize}
\item \textbf{97个库和包}：支持量化研究与实盘交易
\item \textbf{696条策略}：来自机构与学术界的量化策略
\item \textbf{23个视频}：高质量量化学习视频
\item \textbf{14种编程语言}：基于不同编程语言的量化框架
\item \textbf{研究成果复现}：代码复现经典研究成果
\item 同时还有一些有价值的博客与课程
\end{itemize}

\section{库和包}

下列97个库与包可用于实现交易机器人、回测器、技术指标工具、定价器等。每个库按照其使用的编程语言分类，并根据项目在GitHub上的人气（Star数）从高到低排列。

\subsection{回测和实盘交易}

\subsubsection{通用 - 事件驱动框架}

事件驱动框架是量化交易系统的核心，它们能够处理实时市场数据并执行交易决策。

\paragraph{主要Python框架}

\begin{itemize}
\item \textbf{vnpy}：基于Python的开源量化交易系统开发框架，2015年1月正式发布，从零成长为一个功能完善的量化交易平台
\item \textbf{zipline}：一个Pythonic的算法交易库，基于事件驱动模型进行回测
\item \textbf{backtrader}：事件驱动的Python回测库，用于交易策略
\item \textbf{QUANTAXIS}：支持任务调度、分布式部署的股票/期货/期权/港股/加密货币数据、回测、模拟、交易、可视化和多账户的纯本地量化解决方案
\item \textbf{QuantConnect Lean}：QuantConnect提供的Lean算法交易引擎（Python、C\#）
\item \textbf{Rqalpha}：一个可扩展、可替换的Python算法回测\&交易框架，支持多种金融产品
\item \textbf{finmarketpy}：一个Python库，用于回测交易策略与分析金融市场
\item \textbf{backtesting.py}：在历史数据上推断交易策略可行性的Python回测框架，相比现有替代品更轻量、快速、易用、直观、交互性强
\end{itemize}

\paragraph{其他语言框架}

\begin{itemize}
\item \textbf{PandoraTrader}：基于C++开发的高频量化交易平台，支持多种交易API且跨平台
\item \textbf{gobacktest}：使用Go语言实现的事件驱动回测框架
\item \textbf{FlashFunk}：用Rust编写的高性能运行时
\end{itemize}

\subsubsection{通用 - 向量化框架}

向量化框架通过批量处理数据来提高计算效率，特别适合大规模策略测试。

\begin{itemize}
\item \textbf{vectorbt}：完全基于pandas和NumPy，并使用Numba加速的回测工具，可在高速和规模化前提下测试成千上万策略
\item \textbf{pysystemtrade}：《Systematic Trading》作者Rob Carver提供的Python系统化交易实现
\item \textbf{bt}：基于Python的灵活回测库，使用类似树形的策略结构
\end{itemize}

\subsubsection{加密货币专用框架}

加密货币市场的特殊性催生了专门的交易框架。

\begin{itemize}
\item \textbf{Freqtrade}：一个使用Python编写的免费开源加密货币交易机器人，支持各大交易所并可通过Telegram控制，提供回测、可视化和资金管理，支持机器学习优化策略
\item \textbf{Jesse}：旨在简化交易策略研究与开发的高级加密货币交易框架
\item \textbf{OctoBot}：支持技术分析、套利与社交化交易等功能的加密货币交易机器人，配备高级Web界面
\item \textbf{Kelp}：Stellar DEX与100+中心化交易所的免费开源交易机器人
\item \textbf{Hummingbot}：一款专注于市场做市（market making）的加密货币交易客户端
\end{itemize}

\subsection{交易机器人}

交易机器人与alpha模型，有些项目可能已过时或无人维护。

\begin{itemize}
\item \textbf{Blackbird}：比特币跨平台套利机器人，做多/做空的市场中性策略
\item \textbf{bitcoin-arbitrage}：比特币套利机会探测器
\item \textbf{ThetaGang}：针对IBKR的收租策略(theta)机器人
\item \textbf{czsc}：「缠中说禅」技术分析工具；缠论；股票/期货/Quant/量化交易
\item \textbf{R2 Bitcoin Arbitrager}：基于Node.js + TypeScript的自动化比特币套利交易系统
\item \textbf{PyTrendFollow}：使用趋势跟随方法进行系统化期货交易
\end{itemize}

\subsection{分析工具}

\subsubsection{技术指标}

预测未来价格运动的指标库。

\begin{itemize}
\item \textbf{ta-lib}：对金融市场数据进行技术分析的经典库
\item \textbf{go-tart}：Go语言版本的ta-lib，支持流式更新
\item \textbf{pandas-ta}：Pandas技术分析扩展，包含130+技术指标与60多种TA-Lib K线形态
\item \textbf{finta}：常见金融技术指标的Pandas实现
\item \textbf{ta-rust}：Rust语言的技术分析库
\end{itemize}

\subsubsection{指标计算}

计算各种金融指标的库。

\begin{itemize}
\item \textbf{quantstats}：为量化研究提供投资组合分析的Python库
\item \textbf{ffn}：一个Python金融函数库
\end{itemize}

\subsubsection{优化}

投资组合优化和参数优化工具。

\begin{itemize}
\item \textbf{PyPortfolioOpt}：金融投资组合优化
\item \textbf{Riskfolio-Lib}：投资组合优化和量化战略风险管理库
\item \textbf{cvxpy}：凸优化的Python嵌入式建模语言
\end{itemize}

\subsubsection{定价}

金融工具定价模型。

\begin{itemize}
\item \textbf{QuantLib}：免费/开源的量化金融库
\item \textbf{tf-quant-finance}：TensorFlow中的高性能量化金融库
\item \textbf{ficc}：固定收益常见计算
\end{itemize}

\subsubsection{风险管理}

风险评估和管理工具。

\begin{itemize}
\item \textbf{pyfolio}：投资组合和风险分析
\item \textbf{empyrical}：常见金融风险和绩效指标
\item \textbf{riskparityportfolio}：快速设计风险平价投资组合
\end{itemize}

\subsection{券商API}

连接各大券商的API接口。

\begin{itemize}
\item \textbf{ib\_insync}：Interactive Brokers的Python API
\item \textbf{alpaca-trade-api}：Alpaca的Python客户端
\item \textbf{robin-stocks}：Robinhood的Python库
\item \textbf{tda-api}：TD Ameritrade的非官方API封装
\end{itemize}

\subsection{数据源}

\subsubsection{通用数据源}

\begin{itemize}
\item \textbf{yfinance}：从Yahoo! Finance下载市场数据
\item \textbf{pandas-datareader}：从各种网络数据源提取数据
\item \textbf{quandl}：获取金融和经济数据
\item \textbf{alpha\_vantage}：Alpha Vantage API的Python封装
\item \textbf{financialmodelingprep}：Financial Modeling Prep API
\end{itemize}

\subsubsection{加密货币数据源}

\begin{itemize}
\item \textbf{ccxt}：连接和交易超过100个加密货币交易所
\item \textbf{python-binance}：Binance交易所API的Python封装
\item \textbf{coinbasepro-python}：Coinbase Pro API的官方Python库
\end{itemize}

\subsection{数据科学}

量化分析中常用的数据科学工具。

\begin{itemize}
\item \textbf{pandas}：强大的数据分析和操作库
\item \textbf{numpy}：科学计算的基础包
\item \textbf{scipy}：科学计算库
\item \textbf{scikit-learn}：机器学习库
\item \textbf{statsmodels}：统计建模
\end{itemize}

\subsection{数据库}

存储和管理金融数据的数据库解决方案。

\begin{itemize}
\item \textbf{Arctic}：MongoDB的高性能数据存储
\item \textbf{InfluxDB}：时间序列数据库
\item \textbf{TimescaleDB}：PostgreSQL的时间序列扩展
\item \textbf{kdb+}：高性能时间序列数据库
\end{itemize}

\subsection{机器学习}

应用于量化交易的机器学习工具。

\begin{itemize}
\item \textbf{scikit-learn}：通用机器学习库
\item \textbf{tensorflow}：深度学习框架
\item \textbf{pytorch}：深度学习框架
\item \textbf{xgboost}：梯度提升框架
\item \textbf{lightgbm}：梯度提升框架
\end{itemize}

\subsection{时间序列分析}

专门用于时间序列数据分析的工具。

\begin{itemize}
\item \textbf{statsmodels}：统计建模，包含时间序列分析
\item \textbf{arch}：ARCH和GARCH模型
\item \textbf{pyflux}：时间序列建模库
\item \textbf{prophet}：Facebook的时间序列预测工具
\end{itemize}

\subsection{可视化}

数据可视化和图表工具。

\begin{itemize}
\item \textbf{matplotlib}：Python绘图库
\item \textbf{seaborn}：统计数据可视化
\item \textbf{plotly}：交互式图表
\item \textbf{bokeh}：交互式可视化
\item \textbf{mplfinance}：金融数据可视化
\end{itemize}

\section{学习建议}

\subsection{初学者路径}

\begin{enumerate}
\item \textbf{基础知识}：学习Python编程、统计学、金融市场基础
\item \textbf{数据处理}：掌握pandas、numpy等数据处理工具
\item \textbf{简单策略}：从移动平均、RSI等简单策略开始
\item \textbf{回测框架}：学习使用backtrader或backtesting.py
\item \textbf{风险管理}：了解基本的风险控制方法
\end{enumerate}

\subsection{进阶路径}

\begin{enumerate}
\item \textbf{高级策略}：学习因子模型、配对交易、套利策略
\item \textbf{机器学习}：将ML技术应用到量化交易
\item \textbf{高频交易}：了解市场微观结构和高频策略
\item \textbf{实盘交易}：从模拟交易过渡到实盘交易
\item \textbf{系统架构}：构建完整的量化交易系统
\end{enumerate}

\subsection{实践建议}

\begin{itemize}
\item \textbf{从简单开始}：不要一开始就尝试复杂策略
\item \textbf{重视回测}：充分的历史回测是策略验证的基础
\item \textbf{风险控制}：永远把风险管理放在第一位
\item \textbf{持续学习}：量化交易是一个不断学习的过程
\item \textbf{社区参与}：加入量化交易社区，与同行交流
\end{itemize}

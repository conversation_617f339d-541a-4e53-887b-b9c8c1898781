% 量化交易员带你入门章节
% 基于 quant-wiki/docs/start/ 内容编译

\section{概述}

本章将从量化交易员的专业角度，深入介绍量化投资中的核心概念和实用技能。我们将从基础的风险收益指标开始，逐步深入到复杂的投资策略和风险管理技术。

\section{夏普比率：风险调整后收益的核心指标}

\subsection{为什么不能只看收益率？}

在直觉上，我们常常把"收益率"视为衡量一个投资好坏的核心指标，但现实投资中，"收益率"只是故事的一半。

\subsubsection{案例：绿色投资 vs. 黑色投资}

考虑两种投资策略：
\begin{itemize}
\item \textbf{绿色投资}：最终总收益40\%，过程相对平稳
\item \textbf{黑色投资}：最终总收益同样是40\%，但曲线剧烈波动
\end{itemize}

两者最终收益看似相同，却并不代表它们"质量"一致。黑色投资过程中大起大落，带来以下问题：

\begin{enumerate}
\item \textbf{心理压力}：如若中途出现大回撤，投资人会恐慌，甚至可能提前割肉。
\item \textbf{信心不足}：波动越大，未来越难预测；而平稳向上的绿色更具可预期性。
\item \textbf{流动性需求}：万一在黑色投资大幅回撤时需要现金，就可能不得不在较低价位卖出，造成实际亏损。
\end{enumerate}

\textbf{结论}：在拥有同样"最终收益"的情况下，\textbf{更稳定}的投资显然更受欢迎。这就引出了"风险调整后收益"的概念——我们需要让"波动"或"风险"在回报测度中占有一席之地。

\subsection{用波动率量化"风险"}

要想在衡量回报时考虑风险，先得量化风险。\textbf{标准差（Standard Deviation）}或\textbf{波动率（Volatility）}便是投资中最常见的风险度量方式，记作$\sigma$。

其数学定义为：

\begin{equation}
\sigma = \sqrt{\frac{1}{n-1} \sum_{t=1}^{n} \bigl(r_t - \bar{r}\bigr)^2}
\end{equation}

其中：
\begin{itemize}
\item $r_t$：投资在第$t$个时段的收益率
\item $\bar{r}$：收益率的平均值
\item $n$：统计的周期数（如天、周、月等）
\end{itemize}

通俗地说，波动率就是"收益围绕均值的偏离程度"。波动率越大，说明投资的"走向"越不稳定，风险也越高。

\subsection{夏普比率（Sharpe Ratio）的定义}

当我们有了"波动率"这个衡量风险的工具后，就能定义"风险调整后收益"——即"夏普比率（Sharpe Ratio）"。

\subsubsection{一般形式}

从理论上讲，夏普比率最常见的公式是：

\begin{equation}
\text{Sharpe Ratio} = \frac{\bar{r} - r_f}{\sigma}
\end{equation}

其中：
\begin{itemize}
\item $\bar{r}$：投资的平均收益率
\item $r_f$：无风险收益率（Risk-Free Rate），在某些案例中可能省略或简化
\item $\sigma$：投资收益的波动率
\end{itemize}

不过，在不少量化交易实务中，尤其是短周期（例如日度数据）时，很多人会暂时忽略$r_f$，或者令$r_f \approx 0$。于是，就得到了更简化的形式：

\begin{equation}
\text{Sharpe Ratio} = \frac{\bar{r}}{\sigma}
\end{equation}

\subsubsection{年化夏普比率}

为了方便比较，有时我们还会把夏普比率"年化"。若使用\textbf{日度数据}，通常会乘以$\sqrt{252}$（约252个交易日）：

\begin{equation}
\text{Sharpe Ratio (annualized)} = \frac{\bar{r}_{\text{daily}}}{\sigma_{\text{daily}}} \times \sqrt{252}
\end{equation}

若使用\textbf{月度数据}，则可乘以$\sqrt{12}$来年化，以此类推。

\subsubsection{夏普比率的优势}

\begin{itemize}
\item \textbf{收益越高，夏普比率越高}：反映了收益对指标的正贡献。
\item \textbf{波动越大，夏普比率越低}：越不平稳的投资，被"惩罚"得更严重。
\end{itemize}

所以，夏普比率一举两得：既能鼓励高回报，也能约束高风险。

\subsection{夏普比率的直观例子}

回到最开头"绿色 vs. 黑色"的例子：

\begin{itemize}
\item 如果我们为\textbf{绿色}计算夏普比率，假设结果是2
\item 为\textbf{黑色}计算，假设结果只有0.5
\item 在回报同为40\%的前提下，夏普比率清晰地显示"绿色优于黑色"
\end{itemize}

\textbf{原因}：绿色更平稳，承担更少的风险就拿到了与黑色相同的收益。

\subsection{如何通过组合提升夏普比率？}

\subsubsection{分散化（Diversification）}

分散化是提升夏普比率的经典方法。通过投资多个相关性较低的资产，可以在不显著降低预期收益的情况下，大幅降低投资组合的整体波动率。

\textbf{数学原理}：
假设有两个资产，权重分别为$w_1$和$w_2$（$w_1 + w_2 = 1$），则投资组合的方差为：

\begin{equation}
\sigma_p^2 = w_1^2\sigma_1^2 + w_2^2\sigma_2^2 + 2w_1w_2\sigma_1\sigma_2\rho_{12}
\end{equation}

其中$\rho_{12}$是两个资产的相关系数。当$\rho_{12} < 1$时，投资组合的风险小于各个资产风险的加权平均。

\subsubsection{动态再平衡}

定期调整投资组合权重，卖出表现过好的资产，买入表现相对较差的资产，可以实现"低买高卖"的效果，从而提升长期的风险调整后收益。

\subsubsection{风险预算}

根据各个资产的风险贡献来分配权重，而不是简单的等权重分配。风险平价（Risk Parity）策略就是这种思想的典型应用。

\subsection{夏普比率的局限性}

虽然夏普比率是量化投资中最重要的指标之一，但它也有一些局限性：

\subsubsection{假设收益率正态分布}

夏普比率假设收益率服从正态分布，但实际金融市场中，收益率往往表现出"肥尾"特征，即极端事件发生的概率比正态分布预测的要高。

\subsubsection{忽略高阶矩}

夏普比率只考虑了收益率的前两阶矩（均值和方差），忽略了偏度（skewness）和峰度（kurtosis）等高阶矩信息。

\subsubsection{时间聚合问题}

不同时间频率计算的夏普比率可能不具有可比性，需要进行适当的年化处理。

\subsubsection{对称性假设}

夏普比率将上行波动和下行波动同等对待，但投资者通常更关心下行风险。

\subsection{夏普比率的改进指标}

为了克服夏普比率的局限性，学者和实务界提出了多种改进指标：

\subsubsection{索提诺比率（Sortino Ratio）}

索提诺比率只考虑下行波动，公式为：

\begin{equation}
\text{Sortino Ratio} = \frac{\bar{r} - r_f}{\sigma_d}
\end{equation}

其中$\sigma_d$是下行标准差，只计算低于目标收益率的波动。

\subsubsection{卡尔马比率（Calmar Ratio）}

卡尔马比率使用最大回撤作为风险度量：

\begin{equation}
\text{Calmar Ratio} = \frac{\text{年化收益率}}{\text{最大回撤}}
\end{equation}

\subsubsection{信息比率（Information Ratio）}

信息比率衡量相对于基准的超额收益：

\begin{equation}
\text{Information Ratio} = \frac{\bar{r} - \bar{r}_b}{\sigma_{r-r_b}}
\end{equation}

其中$\bar{r}_b$是基准收益率，$\sigma_{r-r_b}$是超额收益的标准差。

\subsection{实际应用中的注意事项}

\subsubsection{样本期选择}

计算夏普比率时，样本期的选择至关重要。太短的样本期可能无法捕捉到策略的真实特征，太长的样本期可能包含已经失效的历史信息。

\subsubsection{交易成本考虑}

理论计算的夏普比率往往忽略了交易成本，在实际应用中需要考虑佣金、滑点、市场冲击等成本因素。

\subsubsection{数据质量}

高质量的数据是准确计算夏普比率的前提。需要注意数据的完整性、准确性和一致性。

\subsubsection{策略容量}

高夏普比率的策略往往容量有限，随着资金规模的增加，策略的有效性可能会下降。

\subsection{夏普比率在投资组合优化中的应用}

\subsubsection{均值-方差优化}

在马科维茨的均值-方差框架中，最优投资组合是在给定风险水平下收益最大化，或在给定收益水平下风险最小化的组合。这等价于最大化夏普比率。

\subsubsection{Black-Litterman模型}

Black-Litterman模型是对经典均值-方差优化的改进，通过引入投资者的主观观点来调整预期收益，从而得到更稳定的投资组合权重。

\subsubsection{风险平价策略}

风险平价策略要求投资组合中每个资产对总风险的贡献相等，这种方法往往能够获得较高的夏普比率。

\subsection{小结}

夏普比率作为衡量风险调整后收益的核心指标，在量化投资中发挥着重要作用。理解其计算方法、应用场景和局限性，对于量化交易员来说至关重要。在实际应用中，应该结合其他指标和方法，全面评估投资策略的表现。

\section{期权定价：从物理方程到金融创新}

\subsection{从物理与数学到金融市场}

你可能难以置信，一个源于物理与数学的方程竟然能催生多个总规模达\textbf{数万亿美元}的金融产业链。然而，这正是现代金融史中的真实故事。\textbf{期权（Options）}的定价理论与技巧，深受数学、统计、物理以及概率论的影响。通过探索这一交叉领域，我们不但能了解期权定价的起源，更能窥见量化金融如何从理论走向实践，进而改变全球资本市场的运作方式。

\subsection{期权的概念与风险管理}

期权是一种给持有人在未来以特定价格买入或卖出某资产的"权利，但非义务"的金融合约。以欧式看涨期权（Call Option）为例：

\begin{itemize}
\item 当前标的资产价格为$S_0$
\item 行权价（执行价）为$K$
\item 到期日为$T$
\item 若到期时标的价格$S_T > K$，则期权多头可获利$\max(S_T - K,0)$
\item 若$S_T \leq K$，则期权多头选择不执行，损失仅为最初支付的期权费
\end{itemize}

这种合约形式早在公元前600年就被古希腊哲学家\textbf{泰勒斯（Thales）}运用过。他通过支付少量费用提前锁定橄榄榨油机的使用权，一旦橄榄大丰收，他便能从中获利。这种先见之明的风险与机会管理思维，正是期权设计背后的基本逻辑。

\subsection{从随机漫步到期权定价：巴舍利耶的先驱之举}

早期的期权交易全凭交易员的经验和直觉定价。直到1900年，法国数学家路易·巴舍利耶（Louis Bachelier）在其博士论文中提出：股价未来的运动可视为\textbf{对称的随机过程}。他假设在短时间间隔$\Delta t$内，股价变动$\Delta S$满足：

\begin{equation}
\Delta S \sim \sigma \sqrt{\Delta t} Z
\end{equation}

其中$\sigma$为波动率，$Z$为标准正态随机变量。当时间推移，价格变动累积后，股价$S_T$的分布趋近正态：

\begin{equation}
S_T \sim \mathcal{N}(S_0, \sigma^2 T)
\end{equation}

（这是巴舍利耶原始模型的假设，后经改良为对数正态模型。）

通过计算各种未来价格发生的概率，并令期权买卖双方的期望收益相等，巴舍利耶给出了期权的"公平价格"概念。可惜当时这一革命性思想未引起足够关注。

\subsection{布朗运动与爱因斯坦的启示}

有趣的是，巴舍利耶的随机漫步思想与物理学中布朗运动（Brownian Motion）的研究如出一辙。1905年，\textbf{爱因斯坦}证明悬浮微粒的无序运动来自无数无法预测的分子撞击。这在数学上可用维纳过程（Wiener Process）描述：

\begin{equation}
X_t = X_0 + W_t, \quad W_t \sim \mathcal{N}(0,t)
\end{equation}

爱因斯坦的研究间接证明了分子真实存在，并为随机过程提供了坚实的物理基础。这种随机性模型与金融价格的不可预测性不谋而合。

\subsection{从赌桌到华尔街：数学策略的进化}

20世纪中叶，Ed Thorp通过数学策略在赌场的二十一点游戏中获利，然后将类似思路用于股市。他以\textbf{"对冲"（Hedging）}概念为核心，通过动态调整标的资产与期权头寸的比例（Delta Hedging）来降低风险。例如，当持有卖出的看涨期权时，为消除标的价格上涨带来的损失，可对应持有部分标的资产，使总体风险趋近中性。

\subsection{Black-Scholes-Merton：期权定价的经典方程}

1973年，费雪·布莱克（F. Black）、迈伦·斯科尔斯（M. Scholes）和罗伯特·默顿（R. Merton）提出了著名的Black-Scholes公式。在该模型中，股价被设为服从几何布朗运动：

\begin{equation}
S_T = S_0 e^{(r - \frac{\sigma^2}{2})T + \sigma\sqrt{T}Z}
\end{equation}

其中$r$为无风险利率。对于欧式看涨期权，Black-Scholes定价公式为：

\begin{equation}
C_0 = S_0 N(d_1) - K e^{-rT}N(d_2)
\end{equation}

其中

\begin{align}
d_1 &= \frac{\ln(\frac{S_0}{K}) + (r+\frac{\sigma^2}{2})T}{\sigma\sqrt{T}} \\
d_2 &= d_1 - \sigma \sqrt{T}
\end{align}

$N(\cdot)$为标准正态分布的累积分布函数。

Black-Scholes模型为期权定价提供了清晰可计算的标准，促使期权市场快速增长，进而催生了信贷违约掉期、场外衍生品、资产证券化等万亿美元级产业。

\subsection{对冲与杠杆的双面性}

有了明确定价公式，企业与投资者可利用期权和衍生品精准\textbf{对冲风险}。例如，航空公司若担心油价上涨，可购入与油价相关的期权锁定成本。同时，期权提供杠杆效应，让投资者用较少资金获得更大敞口。然而，这种杠杆也会在市场波动时放大风险。正常时期，丰富的衍生品增强市场流动性与稳定性；极端动荡中，高度相关的崩盘会通过衍生品市场加剧整体风险。

\subsection{量化帝国的崛起：吉姆·西蒙斯与文艺复兴科技}

Black-Scholes公式公开后，简单套利机会渐少。以吉姆·西蒙斯（Jim Simons）为首的一批数学家、物理学家，利用更复杂的统计与机器学习方法，从海量数据中挖掘微小但持久的价格模式。文艺复兴科技公司（Renaissance Technologies）的Medallion基金年均回报率高达66\%，令人瞠目，这对市场有效性提出质疑，也彰显了高精尖数学工具的威力。

\subsection{完美效率的悖论}

当越来越多的学者和从业者使用数学模型与算法挖掘市场模式，市场变得更有效率，套利空间更难寻找。理论上，若所有价格规律被穷尽，市场价格将真正成为随机漫步，从而再无轻易盈利的机会。这是量化金融的悖论：\textbf{我们用数学逼近完美市场，同时也在不断减少下一代投资者获取超额收益的潜能。}

\subsection{期权定价模型的发展}

\subsubsection{Black-Scholes模型的假设}

Black-Scholes模型基于以下关键假设：

\begin{enumerate}
\item 标的资产价格服从几何布朗运动
\item 无风险利率恒定
\item 标的资产不支付股息
\item 市场无摩擦（无交易成本、税收等）
\item 可以连续交易
\item 波动率恒定
\item 欧式期权（只能在到期日行权）
\end{enumerate}

\subsubsection{模型的局限性与改进}

尽管Black-Scholes模型具有里程碑意义，但在实际应用中存在一些局限性：

\paragraph{波动率微笑}
实际市场中，不同行权价的期权隐含波动率并不相同，呈现"微笑"形状，这与模型假设的恒定波动率不符。

\paragraph{跳跃风险}
股价可能出现跳跃式变动，而不是连续的几何布朗运动。

\paragraph{利率风险}
长期期权受利率变化影响较大，恒定利率假设不够现实。

为了解决这些问题，学者们提出了多种改进模型：

\begin{itemize}
\item \textbf{Heston模型}：引入随机波动率
\item \textbf{Merton跳跃扩散模型}：考虑价格跳跃
\item \textbf{Hull-White模型}：引入随机利率
\item \textbf{局部波动率模型}：波动率依赖于标的价格和时间
\end{itemize}

\subsection{期权的希腊字母}

期权交易中，风险管理至关重要。希腊字母（Greeks）用来衡量期权价格对各种因素变化的敏感性：

\subsubsection{Delta（$\Delta$）}

Delta衡量期权价格对标的资产价格变化的敏感性：

\begin{equation}
\Delta = \frac{\partial C}{\partial S}
\end{equation}

对于看涨期权，$\Delta \in [0,1]$；对于看跌期权，$\Delta \in [-1,0]$。

\subsubsection{Gamma（$\Gamma$）}

Gamma衡量Delta的变化率：

\begin{equation}
\Gamma = \frac{\partial^2 C}{\partial S^2} = \frac{\partial \Delta}{\partial S}
\end{equation}

\subsubsection{Theta（$\Theta$）}

Theta衡量期权价格对时间流逝的敏感性：

\begin{equation}
\Theta = \frac{\partial C}{\partial t}
\end{equation}

通常为负值，表示时间价值的衰减。

\subsubsection{Vega（$\nu$）}

Vega衡量期权价格对波动率变化的敏感性：

\begin{equation}
\nu = \frac{\partial C}{\partial \sigma}
\end{equation}

\subsubsection{Rho（$\rho$）}

Rho衡量期权价格对利率变化的敏感性：

\begin{equation}
\rho = \frac{\partial C}{\partial r}
\end{equation}

\subsection{期权交易策略}

\subsubsection{基本策略}

\paragraph{买入看涨期权（Long Call）}
适用于看涨标的资产，风险有限，收益无限。

\paragraph{卖出看涨期权（Short Call）}
适用于看跌或中性标的资产，收益有限，风险无限。

\paragraph{买入看跌期权（Long Put）}
适用于看跌标的资产，风险有限，收益有限。

\paragraph{卖出看跌期权（Short Put）}
适用于看涨或中性标的资产，收益有限，风险较大。

\subsubsection{组合策略}

\paragraph{跨式组合（Straddle）}
同时买入相同行权价和到期日的看涨和看跌期权，适用于预期大幅波动但方向不确定的情况。

\paragraph{宽跨式组合（Strangle）}
买入不同行权价但相同到期日的看涨和看跌期权，成本较低但需要更大的价格变动才能盈利。

\paragraph{蝶式价差（Butterfly Spread）}
通过买入和卖出不同行权价的期权构建，适用于预期价格在特定区间内波动的情况。

\subsection{结语}

从巴舍利耶的期权定价雏形，到爱因斯坦的布朗运动，再到Black-Scholes公式的问世与广泛应用，数学与物理为金融市场提供了理解与定价风险的思路。量化金融让风险管理工具更加完善，创造万亿美元级市场。然而，随着市场朝有效率和透明化方向发展，不断提高的"智力门槛"也减少了套利机会。

在无限逼近完美的道路上，我们见证了科学与金融交织下的伟大创造与自我调适。这正是量化金融的魅力所在：它的终点，也许是一个无差别的随机世界，但在到达终点的过程中，人类以数学和科学之名，彻底改写了金融的游戏规则。

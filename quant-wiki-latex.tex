\documentclass[12pt,a4paper,twoside]{book}

% ==================== 包引用 ====================
% 中文支持
\usepackage[UTF8]{ctex}
\usepackage{xeCJK}

% 页面布局
\usepackage[top=2.5cm,bottom=2.5cm,left=3cm,right=2.5cm]{geometry}
\usepackage{fancyhdr}

% 数学公式
\usepackage{amsmath,amssymb,amsthm}
\usepackage{mathtools}

% 图片和表格
\usepackage{graphicx}
\usepackage{float}
\usepackage{booktabs}
\usepackage{longtable}
\usepackage{array}

% 颜色和链接
\usepackage{xcolor}
\usepackage[colorlinks=true,linkcolor=blue,citecolor=red,urlcolor=blue]{hyperref}

% 代码高亮
\usepackage{listings}
\usepackage{minted}

% 其他实用包
\usepackage{enumerate}
\usepackage{enumitem}
\usepackage{multicol}
\usepackage{caption}
\usepackage{subcaption}
\usepackage{tikz}
\usepackage{pgfplots}

% 参考文献
\usepackage[backend=biber,style=numeric,sorting=none]{biblatex}

% ==================== 文档设置 ====================
% 设置中文字体
\setCJKmainfont{Songti SC}
\setCJKsansfont{Heiti SC}
\setCJKmonofont{STSong}

% 页眉页脚设置
\pagestyle{fancy}
\fancyhf{}
\fancyhead[LE]{\leftmark}
\fancyhead[RO]{\rightmark}
\fancyfoot[C]{\thepage}
\renewcommand{\headrulewidth}{0.4pt}

% 章节标题格式 - 使用默认设置

% 代码样式设置
\lstset{
    basicstyle=\ttfamily\small,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{gray},
    stringstyle=\color{red},
    numbers=left,
    numberstyle=\tiny\color{gray},
    stepnumber=1,
    numbersep=5pt,
    backgroundcolor=\color{gray!10},
    frame=single,
    rulecolor=\color{black!30},
    tabsize=4,
    captionpos=b,
    breaklines=true,
    breakatwhitespace=false,
    showspaces=false,
    showstringspaces=false,
    showtabs=false
}

% 定理环境
\newtheorem{definition}{定义}[chapter]
\newtheorem{theorem}{定理}[chapter]
\newtheorem{lemma}{引理}[chapter]
\newtheorem{corollary}{推论}[chapter]
\newtheorem{example}{例子}[chapter]

% 自定义命令
\newcommand{\email}[1]{\href{mailto:#1}{#1}}
\newcommand{\website}[1]{\href{#1}{#1}}

% ==================== 文档信息 ====================
\title{\Huge\textbf{Quant Wiki 中文量化百科}}
\author{LLMQuant 社区}
\date{\today}

% ==================== 文档开始 ====================
\begin{document}

% 封面页
\frontmatter
\maketitle

% 版权页
\newpage
\thispagestyle{empty}
\vspace*{\fill}
\begin{center}
\textbf{版权声明}\\[1em]
本文档基于 Quant Wiki 开源项目编译而成\\
原项目地址：\website{https://github.com/LLMQuant/quant-wiki}\\
网站地址：\website{https://quant-wiki.com}\\[2em]

\textbf{许可协议}\\[1em]
署名-非商业性使用-相同方式共享 4.0 国际 (CC BY-NC-SA 4.0)\\
详见：\website{https://creativecommons.org/licenses/by-nc-sa/4.0/}\\[2em]

\textbf{编译日期}\\[1em]
\today\\[2em]

\textbf{致谢}\\[1em]
感谢 LLMQuant 社区的所有贡献者\\
感谢 OI Wiki 项目的启发和指导
\end{center}
\vspace*{\fill}

% 目录
\tableofcontents
\listoffigures
\listoftables

% 前言
\chapter*{前言}
\addcontentsline{toc}{chapter}{前言}

我们致力于量化知识的\textbf{开源}与\textbf{汉化}，打破国内外量化金融行业信息差。

量化投资（Quantitative Investing，简称 Quant）是一种以数学模型、统计分析和算法为基础的投资方式，是现代金融领域的重要分支。

量化交易（Quantitative Trading）由对冲基金、投行等机构主导，以数据驱动的方式进行市场分析和交易决策。借助计算机和算法，量化交易能够高效处理海量数据，快速捕捉市场中的套利机会和趋势。在\textbf{人工智能}飞速发展的今天，如何结合最新的AI技术，提升量化交易效率，是每个量化投资者需要思考的问题，也是我们努力的方向。

\textbf{Quant Wiki} 致力于打造一个\textbf{免费开放}、\textbf{持续更新}的量化金融（quantitative finance）知识分享平台。在这里，大家可以学习量化交易的核心知识、常用模型、算法设计，以及交易中的实战策略。我们为大家准备了丰富的资料，包括因子模型、事件驱动策略、执行成本优化等内容，帮助大家快速掌握量化投资领域的核心技能，迈向专业化道路。

本项目由 LLMQuant 社区提供资金与技术支持，欢迎大家贡献与支持。

% 正文开始
\mainmatter

% ==================== 第一部分：基础知识 ====================
\part{基础知识}

% 第1章：金融术语
\chapter{金融术语}
\input{chapters/finance-terms}

% 第2章：概率基础
\chapter{概率基础}
% \input{chapters/probability-basics}
本章内容待补充。

% 第3章：统计基础
\chapter{统计基础}
% \input{chapters/statistics-basics}
本章内容待补充。

% 第4章：量化术语
\chapter{量化术语}
% \input{chapters/quant-terms}
本章内容待补充。

% ==================== 第二部分：入门教程 ====================
\part{入门教程}

% 第5章：量化交易员带你入门
\chapter{量化交易员带你入门}
\input{chapters/quant-trader-intro}

% 第6章：必懂概念入门
\chapter{必懂概念入门}
% \input{chapters/essential-concepts}
本章内容待补充。

% 第7章：策略类型入门
\chapter{策略类型入门}
% \input{chapters/strategy-types}
本章内容待补充。

% 第8章：实用行业入门
\chapter{实用行业入门}
% \input{chapters/industry-intro}
本章内容待补充。

% ==================== 第三部分：前沿技术 ====================
\part{前沿技术}

% 第9章：量化最新研究
\chapter{量化最新研究}
\input{chapters/latest-research}

% 第10章：研报精选
\chapter{研报精选}
% \input{chapters/research-reports}
本章内容待补充。

% ==================== 第四部分：AI与量化 ====================
\part{AI与量化}

% 第11章：量化与人工智能结合
\chapter{量化与人工智能结合}
\input{chapters/ai-quant-integration}

% 第12章：AI量化论文精选
\chapter{AI量化论文精选}
% \input{chapters/ai-quant-papers}
本章内容待补充。

% 第13章：人工智能前沿
\chapter{人工智能前沿}
% \input{chapters/ai-frontiers}
本章内容待补充。

% ==================== 第五部分：实用资源 ====================
\part{实用资源}

% 第14章：量化学习资源
\chapter{量化学习资源}
\input{chapters/learning-resources}

% 第15章：量化图书馆
\chapter{量化图书馆}
% \input{chapters/quant-library}
本章内容待补充。

% 第16章：研究成果复现
\chapter{研究成果复现}
% \input{chapters/research-reproduction}
本章内容待补充。

% ==================== 第六部分：行业洞察 ====================
\part{行业洞察}

% 第17章：行业内幕
\chapter{行业内幕}
% \input{chapters/industry-insights}
本章内容待补充。

% 第18章：求职专区
\chapter{求职专区}
\input{chapters/career-guidance}

% ==================== 附录 ====================
\appendix

\chapter{学术论文清单}
% \input{appendices/academic-papers}
本章内容待补充。

\chapter{独家资源}
% \input{appendices/exclusive-resources}
本章内容待补充。

\chapter{参考文献}
\printbibliography[heading=none]

\end{document}

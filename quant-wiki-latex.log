This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.7.18)  18 JUL 2025 01:51
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**quant-wiki-latex.tex
(./quant-wiki-latex.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-07-11>
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/book.cls
Document Class: book 2025/01/22 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/bk12.clo
File: bk12.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count271
\c@chapter=\count272
\c@section=\count273
\c@subsection=\count274
\c@subsubsection=\count275
\c@paragraph=\count276
\c@subparagraph=\count277
\c@figure=\count278
\c@table=\count279
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/ctex.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-07-11 L3 programming layer (loader) 

(/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2025-06-09 L3 backend support: XeTeX
\g__graphics_track_int=\count280
\g__pdfannot_backend_int=\count281
\g__pdfannot_backend_link_int=\count282
))
Package: ctex 2022/07/14 v2.5.10 Chinese adapter in LaTeX (CTEX)

(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
LaTeX Encoding Info:    Redeclaring text command \capitalcedilla (encoding TS1)
 on input line 49.
LaTeX Encoding Info:    Redeclaring text command \capitalogonek (encoding TS1) 
on input line 52.
LaTeX Encoding Info:    Redeclaring text command \capitalgrave (encoding TS1) o
n input line 55.
LaTeX Encoding Info:    Redeclaring text command \capitalacute (encoding TS1) o
n input line 56.
LaTeX Encoding Info:    Redeclaring text command \capitalcircumflex (encoding T
S1) on input line 57.
LaTeX Encoding Info:    Redeclaring text command \capitaltilde (encoding TS1) o
n input line 58.
LaTeX Encoding Info:    Redeclaring text command \capitaldieresis (encoding TS1
) on input line 59.
LaTeX Encoding Info:    Redeclaring text command \capitalhungarumlaut (encoding
 TS1) on input line 60.
LaTeX Encoding Info:    Redeclaring text command \capitalring (encoding TS1) on
 input line 61.
LaTeX Encoding Info:    Redeclaring text command \capitalcaron (encoding TS1) o
n input line 62.
LaTeX Encoding Info:    Redeclaring text command \capitalbreve (encoding TS1) o
n input line 63.
LaTeX Encoding Info:    Redeclaring text command \capitalmacron (encoding TS1) 
on input line 64.
LaTeX Encoding Info:    Redeclaring text command \capitaldotaccent (encoding TS
1) on input line 65.
LaTeX Encoding Info:    Redeclaring text command \t (encoding TS1) on input lin
e 66.
LaTeX Encoding Info:    Redeclaring text command \capitaltie (encoding TS1) on 
input line 67.
LaTeX Encoding Info:    Redeclaring text command \newtie (encoding TS1) on inpu
t line 68.
LaTeX Encoding Info:    Redeclaring text command \capitalnewtie (encoding TS1) 
on input line 69.
LaTeX Encoding Info:    Redeclaring text symbol \textcapitalcompwordmark (encod
ing TS1) on input line 70.
LaTeX Encoding Info:    Redeclaring text symbol \textascendercompwordmark (enco
ding TS1) on input line 71.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightbase (encodin
g TS1) on input line 72.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightdblbase (enco
ding TS1) on input line 73.
LaTeX Encoding Info:    Redeclaring text symbol \texttwelveudash (encoding TS1)
 on input line 74.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequartersemdash (encod
ing TS1) on input line 75.
LaTeX Encoding Info:    Redeclaring text symbol \textleftarrow (encoding TS1) o
n input line 76.
LaTeX Encoding Info:    Redeclaring text symbol \textrightarrow (encoding TS1) 
on input line 77.
LaTeX Encoding Info:    Redeclaring text symbol \textblank (encoding TS1) on in
put line 78.
LaTeX Encoding Info:    Redeclaring text symbol \textdollar (encoding TS1) on i
nput line 79.
LaTeX Encoding Info:    Redeclaring text symbol \textquotesingle (encoding TS1)
 on input line 80.
LaTeX Encoding Info:    Redeclaring text command \textasteriskcentered (encodin
g TS1) on input line 81.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphen (encoding TS1) o
n input line 92.
LaTeX Encoding Info:    Redeclaring text symbol \textfractionsolidus (encoding 
TS1) on input line 93.
LaTeX Encoding Info:    Redeclaring text symbol \textzerooldstyle (encoding TS1
) on input line 94.
LaTeX Encoding Info:    Redeclaring text symbol \textoneoldstyle (encoding TS1)
 on input line 95.
LaTeX Encoding Info:    Redeclaring text symbol \texttwooldstyle (encoding TS1)
 on input line 96.
LaTeX Encoding Info:    Redeclaring text symbol \textthreeoldstyle (encoding TS
1) on input line 97.
LaTeX Encoding Info:    Redeclaring text symbol \textfouroldstyle (encoding TS1
) on input line 98.
LaTeX Encoding Info:    Redeclaring text symbol \textfiveoldstyle (encoding TS1
) on input line 99.
LaTeX Encoding Info:    Redeclaring text symbol \textsixoldstyle (encoding TS1)
 on input line 100.
LaTeX Encoding Info:    Redeclaring text symbol \textsevenoldstyle (encoding TS
1) on input line 101.
LaTeX Encoding Info:    Redeclaring text symbol \texteightoldstyle (encoding TS
1) on input line 102.
LaTeX Encoding Info:    Redeclaring text symbol \textnineoldstyle (encoding TS1
) on input line 103.
LaTeX Encoding Info:    Redeclaring text symbol \textlangle (encoding TS1) on i
nput line 104.
LaTeX Encoding Info:    Redeclaring text symbol \textminus (encoding TS1) on in
put line 105.
LaTeX Encoding Info:    Redeclaring text symbol \textrangle (encoding TS1) on i
nput line 106.
LaTeX Encoding Info:    Redeclaring text symbol \textmho (encoding TS1) on inpu
t line 107.
LaTeX Encoding Info:    Redeclaring text symbol \textbigcircle (encoding TS1) o
n input line 108.
LaTeX Encoding Info:    Redeclaring text command \textcircled (encoding TS1) on
 input line 109.
LaTeX Encoding Info:    Redeclaring text symbol \textohm (encoding TS1) on inpu
t line 115.
LaTeX Encoding Info:    Redeclaring text symbol \textlbrackdbl (encoding TS1) o
n input line 116.
LaTeX Encoding Info:    Redeclaring text symbol \textrbrackdbl (encoding TS1) o
n input line 117.
LaTeX Encoding Info:    Redeclaring text symbol \textuparrow (encoding TS1) on 
input line 118.
LaTeX Encoding Info:    Redeclaring text symbol \textdownarrow (encoding TS1) o
n input line 119.
LaTeX Encoding Info:    Redeclaring text symbol \textasciigrave (encoding TS1) 
on input line 120.
LaTeX Encoding Info:    Redeclaring text symbol \textborn (encoding TS1) on inp
ut line 121.
LaTeX Encoding Info:    Redeclaring text symbol \textdivorced (encoding TS1) on
 input line 122.
LaTeX Encoding Info:    Redeclaring text symbol \textdied (encoding TS1) on inp
ut line 123.
LaTeX Encoding Info:    Redeclaring text symbol \textleaf (encoding TS1) on inp
ut line 124.
LaTeX Encoding Info:    Redeclaring text symbol \textmarried (encoding TS1) on 
input line 125.
LaTeX Encoding Info:    Redeclaring text symbol \textmusicalnote (encoding TS1)
 on input line 126.
LaTeX Encoding Info:    Redeclaring text symbol \texttildelow (encoding TS1) on
 input line 127.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphenchar (encoding TS
1) on input line 128.
LaTeX Encoding Info:    Redeclaring text symbol \textasciibreve (encoding TS1) 
on input line 129.
LaTeX Encoding Info:    Redeclaring text symbol \textasciicaron (encoding TS1) 
on input line 130.
LaTeX Encoding Info:    Redeclaring text symbol \textacutedbl (encoding TS1) on
 input line 131.
LaTeX Encoding Info:    Redeclaring text symbol \textgravedbl (encoding TS1) on
 input line 132.
LaTeX Encoding Info:    Redeclaring text symbol \textdagger (encoding TS1) on i
nput line 133.
LaTeX Encoding Info:    Redeclaring text symbol \textdaggerdbl (encoding TS1) o
n input line 134.
LaTeX Encoding Info:    Redeclaring text symbol \textbardbl (encoding TS1) on i
nput line 135.
LaTeX Encoding Info:    Redeclaring text symbol \textperthousand (encoding TS1)
 on input line 136.
LaTeX Encoding Info:    Redeclaring text symbol \textbullet (encoding TS1) on i
nput line 137.
LaTeX Encoding Info:    Redeclaring text symbol \textcelsius (encoding TS1) on 
input line 138.
LaTeX Encoding Info:    Redeclaring text symbol \textdollaroldstyle (encoding T
S1) on input line 139.
LaTeX Encoding Info:    Redeclaring text symbol \textcentoldstyle (encoding TS1
) on input line 140.
LaTeX Encoding Info:    Redeclaring text symbol \textflorin (encoding TS1) on i
nput line 141.
LaTeX Encoding Info:    Redeclaring text symbol \textcolonmonetary (encoding TS
1) on input line 142.
LaTeX Encoding Info:    Redeclaring text symbol \textwon (encoding TS1) on inpu
t line 143.
LaTeX Encoding Info:    Redeclaring text symbol \textnaira (encoding TS1) on in
put line 144.
LaTeX Encoding Info:    Redeclaring text symbol \textguarani (encoding TS1) on 
input line 145.
LaTeX Encoding Info:    Redeclaring text symbol \textpeso (encoding TS1) on inp
ut line 146.
LaTeX Encoding Info:    Redeclaring text symbol \textlira (encoding TS1) on inp
ut line 147.
LaTeX Encoding Info:    Redeclaring text symbol \textrecipe (encoding TS1) on i
nput line 148.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobang (encoding TS1)
 on input line 149.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobangdown (encoding 
TS1) on input line 150.
LaTeX Encoding Info:    Redeclaring text symbol \textdong (encoding TS1) on inp
ut line 151.
LaTeX Encoding Info:    Redeclaring text symbol \texttrademark (encoding TS1) o
n input line 152.
LaTeX Encoding Info:    Redeclaring text symbol \textpertenthousand (encoding T
S1) on input line 153.
LaTeX Encoding Info:    Redeclaring text symbol \textpilcrow (encoding TS1) on 
input line 154.
LaTeX Encoding Info:    Redeclaring text symbol \textbaht (encoding TS1) on inp
ut line 155.
LaTeX Encoding Info:    Redeclaring text symbol \textnumero (encoding TS1) on i
nput line 156.
LaTeX Encoding Info:    Redeclaring text symbol \textdiscount (encoding TS1) on
 input line 157.
LaTeX Encoding Info:    Redeclaring text symbol \textestimated (encoding TS1) o
n input line 158.
LaTeX Encoding Info:    Redeclaring text symbol \textopenbullet (encoding TS1) 
on input line 159.
LaTeX Encoding Info:    Redeclaring text symbol \textservicemark (encoding TS1)
 on input line 160.
LaTeX Encoding Info:    Redeclaring text symbol \textlquill (encoding TS1) on i
nput line 161.
LaTeX Encoding Info:    Redeclaring text symbol \textrquill (encoding TS1) on i
nput line 162.
LaTeX Encoding Info:    Redeclaring text symbol \textcent (encoding TS1) on inp
ut line 163.
LaTeX Encoding Info:    Redeclaring text symbol \textsterling (encoding TS1) on
 input line 164.
LaTeX Encoding Info:    Redeclaring text symbol \textcurrency (encoding TS1) on
 input line 165.
LaTeX Encoding Info:    Redeclaring text symbol \textyen (encoding TS1) on inpu
t line 166.
LaTeX Encoding Info:    Redeclaring text symbol \textbrokenbar (encoding TS1) o
n input line 167.
LaTeX Encoding Info:    Redeclaring text symbol \textsection (encoding TS1) on 
input line 168.
LaTeX Encoding Info:    Redeclaring text symbol \textasciidieresis (encoding TS
1) on input line 169.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyright (encoding TS1) o
n input line 170.
LaTeX Encoding Info:    Redeclaring text symbol \textordfeminine (encoding TS1)
 on input line 171.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyleft (encoding TS1) on
 input line 172.
LaTeX Encoding Info:    Redeclaring text symbol \textlnot (encoding TS1) on inp
ut line 173.
LaTeX Encoding Info:    Redeclaring text symbol \textcircledP (encoding TS1) on
 input line 174.
LaTeX Encoding Info:    Redeclaring text symbol \textregistered (encoding TS1) 
on input line 175.
LaTeX Encoding Info:    Redeclaring text symbol \textasciimacron (encoding TS1)
 on input line 176.
LaTeX Encoding Info:    Redeclaring text symbol \textdegree (encoding TS1) on i
nput line 177.
LaTeX Encoding Info:    Redeclaring text symbol \textpm (encoding TS1) on input
 line 178.
LaTeX Encoding Info:    Redeclaring text symbol \texttwosuperior (encoding TS1)
 on input line 179.
LaTeX Encoding Info:    Redeclaring text symbol \textthreesuperior (encoding TS
1) on input line 180.
LaTeX Encoding Info:    Redeclaring text symbol \textasciiacute (encoding TS1) 
on input line 181.
LaTeX Encoding Info:    Redeclaring text symbol \textmu (encoding TS1) on input
 line 182.
LaTeX Encoding Info:    Redeclaring text symbol \textparagraph (encoding TS1) o
n input line 183.
LaTeX Encoding Info:    Redeclaring text symbol \textperiodcentered (encoding T
S1) on input line 184.
LaTeX Encoding Info:    Redeclaring text symbol \textreferencemark (encoding TS
1) on input line 185.
LaTeX Encoding Info:    Redeclaring text symbol \textonesuperior (encoding TS1)
 on input line 186.
LaTeX Encoding Info:    Redeclaring text symbol \textordmasculine (encoding TS1
) on input line 187.
LaTeX Encoding Info:    Redeclaring text symbol \textsurd (encoding TS1) on inp
ut line 188.
LaTeX Encoding Info:    Redeclaring text symbol \textonequarter (encoding TS1) 
on input line 189.
LaTeX Encoding Info:    Redeclaring text symbol \textonehalf (encoding TS1) on 
input line 190.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequarters (encoding TS
1) on input line 191.
LaTeX Encoding Info:    Redeclaring text symbol \texteuro (encoding TS1) on inp
ut line 192.
LaTeX Encoding Info:    Redeclaring text symbol \texttimes (encoding TS1) on in
put line 193.
LaTeX Encoding Info:    Redeclaring text symbol \textdiv (encoding TS1) on inpu
t line 194.
))
\l__ctex_tmp_int=\count283
\l__ctex_tmp_box=\box53
\l__ctex_tmp_dim=\dimen149
\g__ctex_section_depth_int=\count284
\g__ctex_font_size_int=\count285

(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)
(/usr/local/texlive/2025/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.st
y
Package: xtemplate 2024-08-16 L3 Experimental prototype document functions
)
\l__xeCJK_tmp_int=\count286
\l__xeCJK_tmp_box=\box54
\l__xeCJK_tmp_dim=\dimen150
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count287
\l__xeCJK_begin_int=\count288
\l__xeCJK_end_int=\count289
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count290
\g__xeCJK_node_int=\count291
\c__xeCJK_CJK_node_dim=\dimen151
\c__xeCJK_CJK-space_node_dim=\dimen152
\c__xeCJK_default_node_dim=\dimen153
\c__xeCJK_CJK-widow_node_dim=\dimen154
\c__xeCJK_normalspace_node_dim=\dimen155
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box55
\l__xeCJK_last_penalty_int=\count292
\l__xeCJK_last_bound_dim=\dimen156
\l__xeCJK_last_kern_dim=\dimen157
\l__xeCJK_widow_penalty_int=\count293

LaTeX template Info: Declaring template type 'xeCJK/punctuation' taking 0
(template)           argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen158
\l__xeCJK_mixed_punct_width_dim=\dimen159
\l__xeCJK_middle_punct_width_dim=\dimen160
\l__xeCJK_fixed_margin_width_dim=\dimen161
\l__xeCJK_mixed_margin_width_dim=\dimen162
\l__xeCJK_middle_margin_width_dim=\dimen163
\l__xeCJK_bound_punct_width_dim=\dimen164
\l__xeCJK_bound_margin_width_dim=\dimen165
\l__xeCJK_margin_minimum_dim=\dimen166
\l__xeCJK_kerning_total_width_dim=\dimen167
\l__xeCJK_same_align_margin_dim=\dimen168
\l__xeCJK_different_align_margin_dim=\dimen169
\l__xeCJK_kerning_margin_width_dim=\dimen170
\l__xeCJK_kerning_margin_minimum_dim=\dimen171
\l__xeCJK_bound_dim=\dimen172
\l__xeCJK_reverse_bound_dim=\dimen173
\l__xeCJK_margin_dim=\dimen174
\l__xeCJK_minimum_bound_dim=\dimen175
\l__xeCJK_kerning_margin_dim=\dimen176
\g__xeCJK_family_int=\count294
\l__xeCJK_fam_int=\count295
\g__xeCJK_fam_allocation_int=\count296
\l__xeCJK_verb_case_int=\count297
\l__xeCJK_verb_exspace_skip=\skip57
 (/usr/local/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count298
\l__fontspec_language_int=\count299
\l__fontspec_strnum_int=\count300
\l__fontspec_tmp_int=\count301
\l__fontspec_tmpa_int=\count302
\l__fontspec_tmpb_int=\count303
\l__fontspec_tmpc_int=\count304
\l__fontspec_em_int=\count305
\l__fontspec_emdef_int=\count306
\l__fontspec_strong_int=\count307
\l__fontspec_strongdef_int=\count308
\l__fontspec_tmpa_dim=\dimen177
\l__fontspec_tmpb_dim=\dimen178
\l__fontspec_tmpc_dim=\dimen179

(/usr/local/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/fontspec/fontspec.cfg)))
(/usr/local/texlive/2025/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen180
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen181

(/usr/local/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count309
\l__zhnum_tmp_int=\count310

(/usr/local/texlive/2025/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese.d
ef
File: ctex-scheme-chinese.def 2022/07/14 v2.5.10 Chinese scheme for generic (CT
EX)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-mac.def
File: ctex-fontset-mac.def 2022/07/14 v2.5.10 macOS fonts definition (CTEX)

(/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-macold.
def
File: ctex-fontset-macold.def 2022/07/14 v2.5.10 macOS fonts definition for Yos
emite or earlier version (CTEX)

Package fontspec Info: 
(fontspec)             Script 'CJK' not explicitly supported within font
(fontspec)             'STSong'. Check the typeset output, and if it is okay
(fontspec)             then ignore this warning. Otherwise a different font
(fontspec)             should be chosen.


Package fontspec Info: 
(fontspec)             Could not resolve font "STKaiti/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "STHeiti/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "STSong/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Font family 'STSong(0)' created for font 'STSong' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={STHeiti},ItalicFont={STKaiti}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"STSong/OT:script=hani;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"STHeiti/OT:script=hani;language=dflt;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"STKaiti/OT:script=hani;language=dflt;"

))) (/usr/local/texlive/2025/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count311
\Gm@cntv=\count312
\c@Gm@tempcnt=\count313
\Gm@bindingoffset=\dimen182
\Gm@wd@mp=\dimen183
\Gm@odd@mp=\dimen184
\Gm@even@mp=\dimen185
\Gm@layoutwidth=\dimen186
\Gm@layoutheight=\dimen187
\Gm@layouthoffset=\dimen188
\Gm@layoutvoffset=\dimen189
\Gm@dimlist=\toks18
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers

\f@nch@headwidth=\skip59
\f@nch@offset@elh=\skip60
\f@nch@offset@erh=\skip61
\f@nch@offset@olh=\skip62
\f@nch@offset@orh=\skip63
\f@nch@offset@elf=\skip64
\f@nch@offset@erf=\skip65
\f@nch@offset@olf=\skip66
\f@nch@offset@orf=\skip67
\f@nch@height=\skip68
\f@nch@footalignment=\skip69
\f@nch@widthL=\skip70
\f@nch@widthC=\skip71
\f@nch@widthR=\skip72
\@temptokenb=\toks19
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2025/06/16 v2.17y AMS math features
\@mathmargin=\skip73

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen190
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen191
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count314
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count315
\leftroot@=\count316
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count317
\DOTSCASE@=\count318
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box56
\strutbox@=\box57
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen192
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count319
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count320
\dotsspace@=\muskip17
\c@parentequation=\count321
\dspbrk@lvl=\count322
\tag@help=\toks21
\row@=\count323
\column@=\count324
\maxfields@=\count325
\andhelp@=\toks22
\eqnshift@=\dimen193
\alignsep@=\dimen194
\tagshift@=\dimen195
\tagwidth@=\dimen196
\totwidth@=\dimen197
\lineht@=\dimen198
\@envbody=\toks23
\multlinegap=\skip74
\multlinetaggap=\skip75
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks25
\thm@bodyfont=\toks26
\thm@headfont=\toks27
\thm@notefont=\toks28
\thm@headpunct=\toks29
\thm@preskip=\skip76
\thm@postskip=\skip77
\thm@headsep=\skip78
\dth@everypar=\toks30
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty
Package: mathtools 2024/10/04 v1.31 mathematical typesetting tools

(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2025/03/01 v4.3b Infix arithmetic (KKT,FJ)
\calc@Acount=\count326
\calc@Bcount=\count327
\calc@Adimen=\dimen199
\calc@Bdimen=\dimen256
\calc@Askip=\skip79
\calc@Bskip=\skip80
LaTeX Info: Redefining \setlength on input line 86.
LaTeX Info: Redefining \addtolength on input line 87.
\calc@Ccount=\count328
\calc@Cskip=\skip81
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty
Package: mhsetup 2021/03/18 v1.4 programming setup (MH)
)
\g_MT_multlinerow_int=\count329
\l_MT_multwidth_dim=\dimen257
\origjot=\skip82
\l_MT_shortvdotswithinadjustabove_dim=\dimen258
\l_MT_shortvdotswithinadjustbelow_dim=\dimen259
\l_MT_above_intertext_sep=\dimen260
\l_MT_below_intertext_sep=\dimen261
\l_MT_above_shortintertext_sep=\dimen262
\l_MT_below_shortintertext_sep=\dimen263
\xmathstrut@box=\box58
\xmathstrut@dim=\dimen264
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen265
\Gin@req@width=\dimen266
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count330
\float@exts=\toks31
\float@box=\box59
\@float@everytoks=\toks32
\@floatcapt=\box60
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen267
\lightrulewidth=\dimen268
\cmidrulewidth=\dimen269
\belowrulesep=\dimen270
\belowbottomsep=\dimen271
\aboverulesep=\dimen272
\abovetopsep=\dimen273
\cmidrulesep=\dimen274
\cmidrulekern=\dimen275
\defaultaddspace=\dimen276
\@cmidla=\count331
\@cmidlb=\count332
\@aboverulesep=\dimen277
\@belowrulesep=\dimen278
\@thisruleclass=\count333
\@lastruleclass=\count334
\@thisrulewidth=\dimen279
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2024-12-18 v4.23 Multi-page Table package (DPC)
\LTleft=\skip83
\LTright=\skip84
\LTpre=\skip85
\LTpost=\skip86
\LTchunksize=\count335
\LTcapwidth=\dimen280
\LT@head=\box61
\LT@firsthead=\box62
\LT@foot=\box63
\LT@lastfoot=\box64
\LT@gbox=\box65
\LT@cols=\count336
\LT@rows=\count337
\c@LT@tables=\count338
\c@LT@chunks=\count339
\LT@p@ftn=\toks33
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2025/06/08 v2.6j Tabular extension package (FMi)
\col@sep=\dimen281
\ar@mcellbox=\box66
\extrarowheight=\dimen282
\NC@list=\toks34
\extratabsurround=\skip87
\backup@length=\skip88
\ar@cellbox=\box67
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.

(/usr/local/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2025-07-12 v7.01o Hypertext links for LaTeX

(/usr/local/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(/usr/local/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/local/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2025-06-21 v2.57 Cross-referencing by name of section

(/usr/local/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.s
ty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/usr/local/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count340
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count341
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen283
\Hy@linkcounter=\count342
\Hy@pagecounter=\count343

(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2025-07-12 v7.01o Hyperref: PDFDocEncoding definition (HO)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count344

(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2025-07-12 v7.01o Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4066.
Package hyperref Info: Option `colorlinks' set `true' on input line 4066.
Package hyperref Info: Hyper figures OFF on input line 4195.
Package hyperref Info: Link nesting OFF on input line 4200.
Package hyperref Info: Hyper index ON on input line 4203.
Package hyperref Info: Plain pages OFF on input line 4210.
Package hyperref Info: Backreferencing OFF on input line 4215.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4462.
\c@Hy@tempcnt=\count345

(/usr/local/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4801.
\XeTeXLinkMargin=\dimen284

(/usr/local/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/usr/local/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count346
\Field@Width=\dimen285
\Fld@charsize=\dimen286
Package hyperref Info: Hyper figures OFF on input line 6078.
Package hyperref Info: Link nesting OFF on input line 6083.
Package hyperref Info: Hyper index ON on input line 6086.
Package hyperref Info: backreferencing OFF on input line 6093.
Package hyperref Info: Link coloring ON on input line 6096.
Package hyperref Info: Link coloring with OCG OFF on input line 6103.
Package hyperref Info: PDF/A mode OFF on input line 6108.
\Hy@abspage=\count347
\c@Item=\count348
\c@Hfootnote=\count349
)
Package hyperref Info: Driver (autodetected): hxetex.

(/usr/local/texlive/2025/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2025-07-12 v7.01o Hyperref driver for XeTeX
\pdfm@box=\box68
\c@Hy@AnnotLevel=\count350
\HyField@AnnotCount=\count351
\Fld@listcount=\count352
\c@bookmark@seq@number=\count353

(/usr/local/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2025-06-21 v1.11 Rerun checks for auxiliary files (HO)

(/usr/local/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
84.
)
\Hy@SectionHShift=\skip89
) (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.sty
\lst@mode=\count354
\lst@gtempboxa=\box69
\lst@token=\toks35
\lst@length=\count355
\lst@currlwidth=\dimen287
\lst@column=\count356
\lst@pos=\count357
\lst@lostspace=\dimen288
\lst@width=\dimen289
\lst@newlines=\count358
\lst@lineno=\count359
\lst@maxwidth=\dimen290

(/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count360
\lst@skipnumbers=\count361
\lst@framebox=\box70
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/listings/listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)

(/usr/local/texlive/2025/texmf-dist/tex/xelatex/xecjk/xeCJK-listings.sty
Package: xeCJK-listings 2022/08/05 v3.9.1 xeCJK patch file for listings
\l__xeCJK_listings_max_char_int=\count362
\l__xeCJK_listings_flag_int=\count363
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/minted/minted.sty
Package: minted 2025/05/14 v3.7.0 Yet another Pygments shim for LaTeX

(/usr/local/texlive/2025/texmf-dist/tex/generic/catchfile/catchfile.sty
Package: catchfile 2019/12/09 v1.8 Catch the contents of a file (HO)

(/usr/local/texlive/2025/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/fvextra/fvextra.sty
Package: fvextra 2025/05/29 v1.13.2 fvextra - extensions and patches for fancyv
rb

(/usr/local/texlive/2025/texmf-dist/tex/latex/fancyvrb/fancyvrb.sty
Package: fancyvrb 2024/01/20 4.5c verbatim text (tvz,hv)
\FV@CodeLineNo=\count364
\FV@InFile=\read2
\FV@TabBox=\box71
\c@FancyVerbLine=\count365
\FV@StepNumber=\count366
\FV@OutFile=\write3
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/upquote/upquote.sty
Package: upquote 2012/04/19 v1.3 upright-quote and grave-accent glyphs in verba
tim

(/usr/local/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/lineno/lineno.sty
Package: lineno 2025/05/13 line numbers on paragraphs v5.5
\linenopenalty=\count367
\output=\toks36
\linenoprevgraf=\count368
\linenumbersep=\dimen291
\linenumberwidth=\dimen292
\c@linenumber=\count369
\c@pagewiselinenumber=\count370
\c@LN@truepage=\count371
\c@internallinenumber=\count372
\c@internallinenumbers=\count373
\quotelinenumbersep=\dimen293
\bframerule=\dimen294
\bframesep=\dimen295
\bframebox=\box72
\@LN@amsmath@ams@eqpen=\count374
)
\c@FancyVerbWriteLine=\count375
\c@FancyVerbBufferLine=\count376
\c@FV@TrueTabGroupLevel=\count377
\c@FV@TrueTabCounter=\count378
\FV@TabBox@Group=\box73
\FV@bgcolorstructbox=\box74
\FV@TmpLength=\skip90
\c@FV@HighlightLinesStart=\count379
\c@FV@HighlightLinesStop=\count380
\FV@LoopCount=\count381
\FV@NCharsBox=\box75
\FV@BreakIndent=\dimen296
\FV@BreakIndentNChars=\count382
\FV@BreakSymbolSepLeft=\dimen297
\FV@BreakSymbolSepLeftNChars=\count383
\FV@BreakSymbolSepRight=\dimen298
\FV@BreakSymbolSepRightNChars=\count384
\FV@BreakSymbolIndentLeft=\dimen299
\FV@BreakSymbolIndentLeftNChars=\count385
\FV@BreakSymbolIndentRight=\dimen300
\FV@BreakSymbolIndentRightNChars=\count386
\c@FancyVerbLineBreakLast=\count387
\FV@LineBox=\box76
\FV@LineIndentBox=\box77
\c@FV@BreakBufferDepth=\count388
\FV@LineWidth=\dimen301
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/latex2pydata/latex2pydata.sty
Package: latex2pydata 2025/03/26 v0.6.0 latex2pydata - write data to file in Py
thon literal format
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks37
\pgfkeys@temptoks=\toks38

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfil
tered.code.tex
\pgfkeys@tmptoks=\toks39
)))
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgfopts/pgfopts.sty
Package: pgfopts 2014/07/10 v2.1a LaTeX package options with pgfkeys
\pgfopts@list@add@a@toks=\toks40
\pgfopts@list@add@b@toks=\toks41
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
)
\c@minted@FancyVerbLineTemp=\count389
\@float@every@listing=\toks42
\c@listing=\count390
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/enumerate.sty
Package: enumerate 2023/07/04 v3.00 enumerate extensions (DPC)
\@enLab=\toks43
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip91
\enit@outerparindent=\dimen302
\enit@toks=\toks44
\enit@inbox=\box78
\enit@count@id=\count391
\enitdp@description=\count392
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2025/05/25 v2.0a multicolumn formatting (FMi)
\c@tracingmulticols=\count393
\mult@box=\box79
\multicol@leftmargin=\dimen303
\c@unbalance=\count394
\c@collectmore=\count395
\doublecol@number=\count396
\multicoltolerance=\count397
\multicolpretolerance=\count398
\full@width=\dimen304
\page@free=\dimen305
\premulticols=\dimen306
\postmulticols=\dimen307
\multicolsep=\skip92
\multicolbaselineskip=\skip93
\partial@page=\box80
\last@line=\box81
\mc@boxedresult=\box82
\maxbalancingoverflow=\dimen308
\mult@rightbox=\box83
\mult@grightbox=\box84
\mult@firstbox=\box85
\mult@gfirstbox=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\@tempa=\box99
\@tempa=\box100
\@tempa=\box101
\@tempa=\box102
\@tempa=\box103
\@tempa=\box104
\@tempa=\box105
\@tempa=\box106
\@tempa=\box107
\@tempa=\box108
\@tempa=\box109
\@tempa=\box110
\@tempa=\box111
\@tempa=\box112
\@tempa=\box113
\@tempa=\box114
\@tempa=\box115
\@tempa=\box116
\@tempa=\box117
\@tempa=\box118
\@tempa=\box119
\@tempa=\box120
\@tempa=\box121
\@tempa=\box122
\c@minrows=\count399
\c@columnbadness=\count400
\c@finalcolumnbadness=\count401
\last@try=\dimen309
\multicolovershoot=\dimen310
\multicolundershoot=\dimen311
\mult@nat@firstbox=\box123
\colbreak@box=\box124
\mc@col@check@num=\count402
\g__mc_curr_col_int=\count403
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(/usr/local/texlive/2025/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen312
\captionmargin=\dimen313
\caption@leftmargin=\dimen314
\caption@rightmargin=\dimen315
\caption@width=\dimen316
\caption@indent=\dimen317
\caption@parindent=\dimen318
\caption@hangindent=\dimen319
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count404
\c@continuedfloat=\count405
Package caption Info: float package is loaded.
Package caption Info: hyperref package is loaded.
Package caption Info: listings package is loaded.
Package caption Info: longtable package is loaded.

(/usr/local/texlive/2025/texmf-dist/tex/latex/caption/ltcaption.sty
Package: ltcaption 2021/01/08 v1.4c longtable captions (AR)
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count406
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count407
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.te
x
\pgfutil@everybye=\toks45
\pgfutil@tempdima=\dimen320
\pgfutil@tempdimb=\dimen321
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box125
) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
\pgf@x=\dimen322
\pgf@y=\dimen323
\pgf@xa=\dimen324
\pgf@ya=\dimen325
\pgf@xb=\dimen326
\pgf@yb=\dimen327
\pgf@xc=\dimen328
\pgf@yc=\dimen329
\pgf@xd=\dimen330
\pgf@yd=\dimen331
\w@pgf@writea=\write4
\r@pgf@reada=\read3
\c@pgf@counta=\count408
\c@pgf@countb=\count409
\c@pgf@countc=\count410
\c@pgf@countd=\count411
\t@pgf@toka=\toks46
\t@pgf@tokb=\toks47
\t@pgf@tokc=\toks48
\pgf@sys@id@count=\count412
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.de
f
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx
.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-p
df.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count413
)))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.
code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count414
\pgfsyssoftpath@bigbuffer@items=\count415
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.
code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen332
\pgfmath@count=\count416
\pgfmath@box=\box126
\pgfmath@toks=\toks49
\pgfmath@stack@operand=\toks50
\pgfmath@stack@operation=\toks51
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.
tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic
.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigo
nometric.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.rando
m.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.compa
rison.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.
code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round
.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.
code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integ
erarithmetics.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count417
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.co
de.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen333
\pgf@picmaxx=\dimen334
\pgf@picminy=\dimen335
\pgf@picmaxy=\dimen336
\pgf@pathminx=\dimen337
\pgf@pathmaxx=\dimen338
\pgf@pathminy=\dimen339
\pgf@pathmaxy=\dimen340
\pgf@xx=\dimen341
\pgf@xy=\dimen342
\pgf@yx=\dimen343
\pgf@yy=\dimen344
\pgf@zx=\dimen345
\pgf@zy=\dimen346
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconst
ruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen347
\pgf@path@lasty=\dimen348
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage
.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen349
\pgf@shorten@start@additional=\dimen350
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.co
de.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box127
\pgf@hbox=\box128
\pgf@layerbox@main=\box129
\pgf@picture@serial@count=\count418
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicst
ate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen351
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransform
ations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen352
\pgf@pt@y=\dimen353
\pgf@pt@temp=\dimen354
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.cod
e.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.c
ode.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathproce
ssing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.co
de.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen355
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.cod
e.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen356
\pgf@sys@shading@range@num=\count419
\pgf@shadingcount=\count420
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.cod
e.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.
code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box130
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.co
de.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretranspare
ncy.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.
code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.
tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.cod
e.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box131
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.
tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version
-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen357
\pgf@nodesepend=\dimen358
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version
-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen359
\pgffor@skip=\dimen360
\pgffor@stack=\toks52
\pgffor@toks=\toks53
))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.cod
e.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothan
dlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count421
\pgfplotmarksize=\dimen361
)
\tikz@lastx=\dimen362
\tikz@lasty=\dimen363
\tikz@lastxsaved=\dimen364
\tikz@lastysaved=\dimen365
\tikz@lastmovetox=\dimen366
\tikz@lastmovetoy=\dimen367
\tikzleveldistance=\dimen368
\tikzsiblingdistance=\dimen369
\tikz@figbox=\box132
\tikz@figbox@bg=\box133
\tikz@tempbox=\box134
\tikz@tempbox@bg=\box135
\tikztreelevel=\count422
\tikznumberofchildren=\count423
\tikznumberofcurrentchild=\count424
\tikz@fig@count=\count425

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.cod
e.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count426
\pgfmatrixcurrentcolumn=\count427
\pgf@matrix@numberofcolumns=\count428
)
\tikz@expandcount=\count429

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/librarie
s/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/local/texlive/2025/texmf-dist/tex/latex/pgfplots/pgfplots.sty
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks54
\t@pgfplots@tokb=\toks55
\t@pgfplots@tokc=\toks56
\pgfplots@tmpa=\dimen370
\c@pgfplots@coordindex=\count430
\c@pgfplots@scanlineindex=\count431

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric
.code.tex))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.c
ode.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplot
soldpgfsupp_loader.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.cod
e.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks57
\t@pgf@tokb=\toks58
\t@pgf@tokc=\toks59

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplot
soldpgfsupp_pgfutil-common-lists.tex))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code
.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/liststructure/pgfplots
liststructure.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/liststructure/pgfplots
liststructureext.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/liststructure/pgfplots
array.code.tex
\c@pgfplotsarray@tmp=\count432
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/liststructure/pgfplots
matrix.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstable
shared.code.tex
\c@pgfplotstable@counta=\count433
\t@pgfplotstable@a=\toks60
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/liststructure/pgfplots
deque.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.co
de.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.da
ta.code.tex))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb
.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplot
s.surfshading.code.tex
\c@pgfplotslibrarysurf@no=\count434

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots
.surfshading.pgfsys-xetex.def
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots
.surfshading.pgfsys-dvipdfmx.def
\c@pgfplotslibrarysurf@streamlen=\count435
))))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.
code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.cod
e.tex))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.c
ode.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.c
ode.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandle
r.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.
code.tex)))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.
tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessin
g.code.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.cod
e.tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.
tex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.te
x)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/librarie
s/tikzlibrarydecorations.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/modules/pgfmoduledecoration
s.code.tex
\pgfdecoratedcompleteddistance=\dimen371
\pgfdecoratedremainingdistance=\dimen372
\pgfdecoratedinputsegmentcompleteddistance=\dimen373
\pgfdecoratedinputsegmentremainingdistance=\dimen374
\pgf@decorate@distancetomove=\dimen375
\pgf@decorate@repeatstate=\count436
\pgfdecorationsegmentamplitude=\dimen376
\pgfdecorationsegmentlength=\dimen377
)
\tikz@lib@dec@box=\box136
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/librarie
s/tikzlibrarydecorations.pathmorphing.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/libraries/decorations/pgfli
brarydecorations.pathmorphing.code.tex))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/librarie
s/tikzlibrarydecorations.pathreplacing.code.tex
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/libraries/decorations/pgfli
brarydecorations.pathreplacing.code.tex))
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplo
ts.contourlua.code.tex)
\pgfplots@numplots=\count437
\pgfplots@xmin@reg=\dimen378
\pgfplots@xmax@reg=\dimen379
\pgfplots@ymin@reg=\dimen380
\pgfplots@ymax@reg=\dimen381
\pgfplots@zmin@reg=\dimen382
\pgfplots@zmax@reg=\dimen383
)
(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/frontendlayer/tikz/librarie
s/tikzlibraryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/local/texlive/2025/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmar
ks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.sty
Package: biblatex 2025/07/10 v3.21 programmable bibliographies (PK/MW)

(/usr/local/texlive/2025/texmf-dist/tex/latex/logreq/logreq.sty
Package: logreq 2010/08/04 v1.0 xml request logger
\lrq@indent=\count438

(/usr/local/texlive/2025/texmf-dist/tex/latex/logreq/logreq.def
File: logreq.def 2010/08/04 v1.0 logreq spec v1.0
))
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\c@tabx@nest=\count439
\c@listtotal=\count440
\c@listcount=\count441
\c@liststart=\count442
\c@liststop=\count443
\c@citecount=\count444
\c@citetotal=\count445
\c@multicitecount=\count446
\c@multicitetotal=\count447
\c@instcount=\count448
\c@maxnames=\count449
\c@minnames=\count450
\c@maxitems=\count451
\c@minitems=\count452
\c@citecounter=\count453
\c@maxcitecounter=\count454
\c@savedcitecounter=\count455
\c@uniquelist=\count456
\c@uniquename=\count457
\c@refsection=\count458
\c@refsegment=\count459
\c@maxextratitle=\count460
\c@maxextratitleyear=\count461
\c@maxextraname=\count462
\c@maxextradate=\count463
\c@maxextraalpha=\count464
\c@abbrvpenalty=\count465
\c@highnamepenalty=\count466
\c@lownamepenalty=\count467
\c@maxparens=\count468
\c@parenlevel=\count469
\blx@tempcnta=\count470
\blx@tempcntb=\count471
\blx@tempcntc=\count472
\c@blx@maxsection=\count473
\blx@maxsegment@0=\count474
\blx@notetype=\count475
\blx@parenlevel@text=\count476
\blx@parenlevel@foot=\count477
\blx@sectionciteorder@0=\count478
\blx@sectionciteorderinternal@0=\count479
\blx@entrysetcounter=\count480
\blx@biblioinstance=\count481
\labelnumberwidth=\skip94
\labelalphawidth=\skip95
\biblabelsep=\skip96
\bibitemsep=\skip97
\bibnamesep=\skip98
\bibinitsep=\skip99
\bibparsep=\skip100
\bibhang=\skip101
\blx@bcfin=\read4
\blx@bcfout=\write5
\blx@langwohyphens=\language90
\c@mincomprange=\count482
\c@maxcomprange=\count483
\c@mincompwidth=\count484
Package biblatex Info: Trying to load biblatex default data model...
Package biblatex Info: ... file 'blx-dm.def' found.

(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-dm.def
File: blx-dm.def 2025/07/10 v3.21 biblatex datamodel (PK/MW)
)
Package biblatex Info: Trying to load biblatex style data model...
Package biblatex Info: ... file 'numeric.dbx' not found.
Package biblatex Info: Trying to load biblatex custom data model...
Package biblatex Info: ... file 'biblatex-dm.cfg' not found.
\c@afterword=\count485
\c@savedafterword=\count486
\c@annotator=\count487
\c@savedannotator=\count488
\c@author=\count489
\c@savedauthor=\count490
\c@bookauthor=\count491
\c@savedbookauthor=\count492
\c@commentator=\count493
\c@savedcommentator=\count494
\c@editor=\count495
\c@savededitor=\count496
\c@editora=\count497
\c@savededitora=\count498
\c@editorb=\count499
\c@savededitorb=\count500
\c@editorc=\count501
\c@savededitorc=\count502
\c@foreword=\count503
\c@savedforeword=\count504
\c@holder=\count505
\c@savedholder=\count506
\c@introduction=\count507
\c@savedintroduction=\count508
\c@namea=\count509
\c@savednamea=\count510
\c@nameb=\count511
\c@savednameb=\count512
\c@namec=\count513
\c@savednamec=\count514
\c@translator=\count515
\c@savedtranslator=\count516
\c@shortauthor=\count517
\c@savedshortauthor=\count518
\c@shorteditor=\count519
\c@savedshorteditor=\count520
\c@labelname=\count521
\c@savedlabelname=\count522
\c@institution=\count523
\c@savedinstitution=\count524
\c@lista=\count525
\c@savedlista=\count526
\c@listb=\count527
\c@savedlistb=\count528
\c@listc=\count529
\c@savedlistc=\count530
\c@listd=\count531
\c@savedlistd=\count532
\c@liste=\count533
\c@savedliste=\count534
\c@listf=\count535
\c@savedlistf=\count536
\c@location=\count537
\c@savedlocation=\count538
\c@organization=\count539
\c@savedorganization=\count540
\c@origlocation=\count541
\c@savedoriglocation=\count542
\c@origpublisher=\count543
\c@savedorigpublisher=\count544
\c@publisher=\count545
\c@savedpublisher=\count546
\c@language=\count547
\c@savedlanguage=\count548
\c@origlanguage=\count549
\c@savedoriglanguage=\count550
\c@pageref=\count551
\c@savedpageref=\count552
\shorthandwidth=\skip102
\shortjournalwidth=\skip103
\shortserieswidth=\skip104
\shorttitlewidth=\skip105
\shortauthorwidth=\skip106
\shorteditorwidth=\skip107
\locallabelnumberwidth=\skip108
\locallabelalphawidth=\skip109
\localshorthandwidth=\skip110
\localshortjournalwidth=\skip111
\localshortserieswidth=\skip112
\localshorttitlewidth=\skip113
\localshortauthorwidth=\skip114
\localshorteditorwidth=\skip115
Package biblatex Info: Trying to load enhanced support for Unicode engines...
Package biblatex Info: ... file 'blx-unicode.def' found.

(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-unicode.def)
Package biblatex Info: Trying to load compatibility code...
Package biblatex Info: ... file 'blx-compat.def' found.

(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-compat.def
File: blx-compat.def 2025/07/10 v3.21 biblatex compatibility (PK/MW)
)
Package biblatex Info: Trying to load generic definitions...
Package biblatex Info: ... file 'biblatex.def' found.

(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.def
File: biblatex.def 2025/07/10 v3.21 biblatex compatibility (PK/MW)
\c@textcitecount=\count553
\c@textcitetotal=\count554
\c@textcitemaxnames=\count555
\c@biburlbigbreakpenalty=\count556
\c@biburlbreakpenalty=\count557
\c@biburlnumpenalty=\count558
\c@biburlucpenalty=\count559
\c@biburllcpenalty=\count560
\biburlbigskip=\muskip19
\biburlnumskip=\muskip20
\biburlucskip=\muskip21
\biburllcskip=\muskip22
\c@smartand=\count561
)
Package biblatex Info: Trying to load bibliography style 'numeric'...
Package biblatex Info: ... file 'numeric.bbx' found.

(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/bbx/numeric.bbx
File: numeric.bbx 2025/07/10 v3.21 biblatex bibliography style (PK/MW)
Package biblatex Info: Trying to load bibliography style 'standard'...
Package biblatex Info: ... file 'standard.bbx' found.

(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/bbx/standard.bbx
File: standard.bbx 2025/07/10 v3.21 biblatex bibliography style (PK/MW)
\c@bbx:relatedcount=\count562
\c@bbx:relatedtotal=\count563
))
Package biblatex Info: Trying to load citation style 'numeric'...
Package biblatex Info: ... file 'numeric.cbx' found.

(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/cbx/numeric.cbx
File: numeric.cbx 2025/07/10 v3.21 biblatex citation style (PK/MW)
Package biblatex Info: Redefining '\cite'.
Package biblatex Info: Redefining '\parencite'.
Package biblatex Info: Redefining '\footcite'.
Package biblatex Info: Redefining '\footcitetext'.
Package biblatex Info: Redefining '\smartcite'.
Package biblatex Info: Redefining '\supercite'.
Package biblatex Info: Redefining '\textcite'.
Package biblatex Info: Redefining '\textcites'.
Package biblatex Info: Redefining '\cites'.
Package biblatex Info: Redefining '\parencites'.
Package biblatex Info: Redefining '\smartcites'.
)
Package biblatex Info: Trying to load configuration file...
Package biblatex Info: ... file 'biblatex.cfg' found.

(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/biblatex.cfg
File: biblatex.cfg 
)
Package biblatex Info: XeTeX detected.
(biblatex)             Assuming input encoding 'utf8'.
Package biblatex Info: Document encoding is UTF8 ....
Package biblatex Info: ... and expl3
(biblatex)             2025-07-11 L3 programming layer (loader) 
(biblatex)             is new enough (at least 2020/04/06),
(biblatex)             setting 'casechanger=expl3'.

(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/blx-case-expl3.sty
Package: blx-case-expl3 2025/07/10 v3.21 expl3 case changing code for biblatex
))

Package xeCJK Warning: Redefining CJKfamily `\CJKrmdefault' (STSong(0)).


Package fontspec Info: 
(fontspec)             Script 'CJK' not explicitly supported within font
(fontspec)             'Songti SC'. Check the typeset output, and if it is
(fontspec)             okay then ignore this warning. Otherwise a different
(fontspec)             font should be chosen.


Package fontspec Info: 
(fontspec)             Could not resolve font "Songti SC/I" (it probably
(fontspec)             doesn't exist).


Package fontspec Info: 
(fontspec)             Font family 'SongtiSC(0)' created for font 'Songti SC'
(fontspec)             with options [Script={CJK}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.: <->"Songti
(fontspec)             SC/OT:script=hani;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.: <->"Songti
(fontspec)             SC/B/OT:script=hani;language=dflt;"
(fontspec)             - 'bold italic' (b/it) with NFSS spec.: <->"Songti
(fontspec)             SC/BI/OT:script=hani;language=dflt;"


Package xeCJK Warning: Redefining CJKfamily `\CJKsfdefault' (STXihei).


Package xeCJK Warning: Redefining CJKfamily `\CJKttdefault' (STFangsong).

\c@definition=\count564
\c@theorem=\count565
\c@lemma=\count566
\c@corollary=\count567
\c@example=\count568
\@quotelevel=\count569
\@quotereset=\count570
(./quant-wiki-latex.aux)
\openout1 = `quant-wiki-latex.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 98.
LaTeX Font Info:    ... okay on input line 98.

Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 98.
LaTeX Font Info:    Redeclaring math accent \acute on input line 98.
LaTeX Font Info:    Redeclaring math accent \grave on input line 98.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 98.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 98.
LaTeX Font Info:    Redeclaring math accent \bar on input line 98.
LaTeX Font Info:    Redeclaring math accent \breve on input line 98.
LaTeX Font Info:    Redeclaring math accent \check on input line 98.
LaTeX Font Info:    Redeclaring math accent \hat on input line 98.
LaTeX Font Info:    Redeclaring math accent \dot on input line 98.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 98.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 98.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 98.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 98.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 98.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 98.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 98.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 98.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 98.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 98.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: twoside 
* h-part:(L,W,R)=(85.35826pt, 441.01773pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=441.01773pt
* \textheight=702.78308pt
* \oddsidemargin=13.08827pt
* \evensidemargin=-1.1381pt
* \topmargin=-33.0119pt
* \headheight=12.0pt
* \headsep=19.8738pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=88.0pt
* \marginparsep=7.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidetrue
* \@mparswitchtrue
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring ON on input line 98.
(./quant-wiki-latex.out) (./quant-wiki-latex.out)
\@outlinefile=\write6
\openout6 = `quant-wiki-latex.out'.

\c@lstlisting=\count571
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.


Package pgfplots Warning: running in backwards compatibility mode (unsuitable t
ick labels; missing features). Consider writing \pgfplotsset{compat=1.18} into 
your preamble.
 on input line 98.

Package biblatex Info: Trying to load language 'english'...
Package biblatex Info: ... file 'english.lbx' found.
(/usr/local/texlive/2025/texmf-dist/tex/latex/biblatex/lbx/english.lbx
File: english.lbx 2025/07/10 v3.21 biblatex localization (PK/MW)
)
Package biblatex Info: XeTeX detected.
(biblatex)             Assuming input encoding 'utf8'.
Package biblatex Info: Automatic encoding selection.
(biblatex)             Assuming data encoding 'utf8'.
\openout5 = `quant-wiki-latex.bcf'.

Package biblatex Info: Trying to load bibliographic data...
Package biblatex Info: ... file 'quant-wiki-latex.bbl' not found.

No file quant-wiki-latex.bbl.
Package biblatex Info: Reference section=0 on input line 98.
Package biblatex Info: Reference segment=0 on input line 98.
LaTeX Font Info:    Trying to load font information for U+msa on input line 103
.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 103
.

(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1



]

[2] (./quant-wiki-latex.toc

[3

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[4]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[5]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[6]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[7])
\tf@toc=\write7
\openout7 = `quant-wiki-latex.toc'.




Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[8] (./quant-wiki-latex.lof)
\tf@lof=\write8
\openout8 = `quant-wiki-latex.lof'.



[9

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[10

] (./quant-wiki-latex.lot)
\tf@lot=\write9
\openout9 = `quant-wiki-latex.lot'.



[11]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[12

]

[13]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[14

]

[1

]

[2]
Chapter 1.
(./chapters/finance-terms.tex

[3

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[4]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[5]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[6]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[7]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[8]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[9]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[10]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[11]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[12]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[13]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[14]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[15]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[16]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[17]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[18]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[19]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[20]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[21]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[22]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[23])


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[24]
Chapter 2.


[25

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[26

]
Chapter 3.


[27]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[28

]
Chapter 4.


[29]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[30

]

[31]

[32]
Chapter 5.
(./chapters/quant-trader-intro.tex

[33

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[34]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[35]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[36]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[37]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[38]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[39]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[40]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[41]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[42])


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[43]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[44

]
Chapter 6.


[45]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[46

]
Chapter 7.


[47]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[48

]
Chapter 8.


[49]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[50

]

[51]

[52]
Chapter 9.
(./chapters/latest-research.tex

[53

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[54]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[55]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[56]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[57]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[58]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[59]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[60]

Package fontspec Info: 
(fontspec)             Script 'CJK' not explicitly supported within font
(fontspec)             'STSong'. Check the typeset output, and if it is okay
(fontspec)             then ignore this warning. Otherwise a different font
(fontspec)             should be chosen.


Package fontspec Info: 
(fontspec)             Could not resolve font "STSong/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "STSong/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Could not resolve font "STSong/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: 
(fontspec)             Font family 'STSong(1)' created for font 'STSong' with
(fontspec)             options [Script={CJK}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"STSong/OT:script=hani;language=dflt;"

 (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstlang1.sty
File: lstlang1.sty 2024/09/23 1.10c listings language file
)
(/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstlang1.sty
File: lstlang1.sty 2024/09/23 1.10c listings language file
)
Package hyperref Info: bookmark level for unknown lstlisting defaults to 0 on i
nput line 294.
LaTeX Font Info:    Font shape `TU/lmtt/bx/n' in size <10.95> not available
(Font)              Font shape `TU/lmtt/b/n' tried instead on input line 305.
)


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[61]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[62

]
Chapter 10.


[63]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[64

]

[65]

[66]
Chapter 11.
(./chapters/ai-quant-integration.tex

[67

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[68] (/usr/local/texlive/2025/texmf-dist/tex/latex/listings/lstlang1.sty
File: lstlang1.sty 2024/09/23 1.10c listings language file
)


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[69]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[70]
Underfull \vbox (badness 10000) has occurred while \output is active []




Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[71]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[72]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[73]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[74]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[75]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[76]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[77]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[78]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[79]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[80])


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[81]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[82

]
Chapter 12.


[83]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[84

]
Chapter 13.


[85]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[86

]

[87]

[88]
Chapter 14.
(./chapters/learning-resources.tex

[89

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[90]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[91]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[92]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[93]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[94]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[95])


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[96]
Chapter 15.


[97

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[98

]
Chapter 16.


[99]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[100

]

[101]

[102]
Chapter 17.


[103

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[104

]
Chapter 18.
(./chapters/career-guidance.tex

[105]
Overfull \hbox (36.5663pt too wide) in paragraph at lines 79--90
 [][] 
 []




Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[106]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[107]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[108]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[109]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[110]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[111]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[112]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[113])


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[114]
附录 A.


[115

]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[116

]
附录 B.


[117]


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 14.49998pt, for example:
(fancyhdr)                \setlength{\headheight}{14.49998pt}.
(fancyhdr)                You might also make \topmargin smaller:
(fancyhdr)                \addtolength{\topmargin}{-2.49998pt}.

[118

]
附录 C.

LaTeX Warning: Empty bibliography on input line 263.



[119]
runsystem(latexminted cleantemp  --timestamp 20250718015108 6DD6D821EBAFDF64CF7
B96D7C58F56FB)...executed safely (allowed).

 (./quant-wiki-latex.aux)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2022/08/05>
 ***********
Package rerunfilecheck Info: File `quant-wiki-latex.out' has not changed.
(rerunfilecheck)             Checksum: 1AB2912FF4AFC1B370D7BB1C9A33E44A;18649.
Package logreq Info: Writing requests to 'quant-wiki-latex.run.xml'.
\openout1 = `quant-wiki-latex.run.xml'.

 ) 
Here is how much of TeX's memory you used:
 51940 strings out of 468457
 1254650 string characters out of 5445913
 2555982 words of memory out of 5000000
 79607 multiletter control sequences out of 15000+600000
 636399 words of font info for 113 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 93i,12n,114p,712b,1991s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on quant-wiki-latex.pdf (133 pages).

body {
    font-family: "LXGW WenKai", sans-serif;
    /* Lite version */
    /* font-family: "LXGW WenKai Lite", sans-serif; */
    /* TC version */
    /* font-family: "LXGW WenKai TC", sans-serif; */
    /* Screen version */
    /* font-family: "LXGW WenKai Screen", sans-serif; */
  }

/* 增大网站标题大小并居中 */
.md-header__title {
    font-size: 1.5rem !important;
    font-weight: 800 !important;
    margin: 0 auto !important;  /* 居中标题 */
    text-align: center !important;
}

/* 调整header布局以支持居中 */
.md-header__inner {
    display: flex;
    justify-content: space-between !important;
    padding: 0 0.1rem;
}

/* 隐藏左侧菜单按钮（可选）
.md-header__button:not(.md-logo) {
    display: block !important;
} */

/* 仅隐藏搜索和其他按钮，保留菜单按钮 */
.md-header__button.md-header__button:not(.md-logo):not(.md-header__button.md-icon) {
    display: none !important;
}

/* 增大导航栏标题大小并居中 */
.md-tabs__link {
    font-size: 1rem !important;
    font-weight: 500 !important;
}

/* 导航栏居中 */
.md-tabs__list {
    display: flex;
    justify-content: center !important;
    margin: 0.5rem auto !important;
}

.md-tabs {
    text-align: center !important;
}

/* 调整返回顶部按钮样式 */
button.md-top {
    font-family: LXGW WenKai;
    font-size: 16px;
    font-weight: bold;
    color: #518FC1;
}

.md-header__button.md-logo img,
.md-header__button.md-logo svg {
    height: 3.5rem;
    width: auto;
}

/* 在这里添加你的自定义样式 */

/* 确保标题在移动端也能正确显示 */
@media screen and (max-width: 76.1875em) {
    .md-header__title {
        margin-left: 0 !important;
        margin-right: 0 !important;
        font-size: 1.2rem !important;  /* 移动端减小字体大小 */
        max-width: calc(100% - 88px) !important;  /* 预留足够空间给菜单按钮 */
        white-space: nowrap !important;  /* 防止标题换行 */
        overflow: hidden !important;  /* 超出部分隐藏 */
        text-overflow: ellipsis !important;  /* 显示省略号 */
    }

    /* 调整移动端header内部布局 */
    .md-header__inner {
        padding: 0 0.2rem !important;
    }
    
    /* 确保菜单按钮可见且不被标题遮挡 */
    .md-header__button.md-icon {
        margin: 0.4rem !important;
        padding: 0.4rem !important;
    }
}
# F.A.Q  

本页面主要解答一些关于量化投资和 Quant-Wiki 开源项目的常见问题。

---

## 我想问点与这个项目相关的问题  

**Q：你们为什么想要创建 Quant-Wiki 项目呢？**  
**A**：你是否在学习量化投资时，面对复杂的金融模型和庞大的数据分析体系感到过迷茫无助？Quant-Wiki 项目的初衷，是为了帮助更多资源不足或初入门槛的学习者，方便地获取优质的量化资源。当然，这背后的动机也很简单——我们希望为量化金融的普及和发展尽一份力，推动更多人探索这个领域。:D

---

**Q：我对这个项目很感兴趣，怎么参与呢？**  
**A**：Quant-Wiki 项目托管在 GitHub 上，您可以直接访问我们的 [repo](https://github.com/LLMQuant/Quant-Wiki) 查看最新进展。参与方式包括：  

1. 在 GitHub 上提交 Issue 或 Pull Request，贡献代码或内容；  
2. 在项目相关的讨论组中分享您的想法，或者直接向维护者投稿；  
3. 帮助我们推广项目，吸引更多志同道合的开发者加入。  

我们使用的工具包括 Python、Jupyter Notebook、Pandas 等，欢迎所有背景的开发者和学习者来参与！

---

**Q：我感觉自己能力不足，不知道能做点什么？**  
**A**：一切源于热爱！无论是审核修改内容、帮助修复简单的错误，还是协助整理文档，都对项目非常重要。没有任何贡献是微不足道的！

---

**Q：现在主要是谁在维护这个项目呢？**  
**A**：最初是一些量化从业者和技术爱好者发起了这个项目，后来吸引了更多有志于量化投资的开发者加入。目前，Quant-Wiki 项目由一群核心维护者共同负责，但我们始终希望更多人能够参与进来，为这个项目注入新的活力。

---

**Q：你们怎么保证项目内容的可持续性呢？**  
**A**：我们将所有内容托管在 GitHub 上，确保即使服务器出现问题，数据也不会丢失。同时，我们会定期备份内容，以确保所有贡献者的努力都能被长期保留。

---

**Q：项目内容好像还有很多空白啊！**  
**A**：确实如此。由于维护者的时间和知识储备有限，一些内容暂时还未完善。所以我们热切地期待更多志同道合的开发者加入，共同完善项目内容。

---

**Q：为什么不直接去更新其他公开的知识平台，比如维基百科呢？**  
**A**：我们希望能更聚焦于量化投资领域，为学习者提供更专业、更易获取的内容。由于一些限制，公开知识平台上的内容不一定对量化投资学习者特别友好，而我们的目标是创建一个针对性的学习资源整合项目。

---

## 我想参与进来  

**Q：我要怎么与项目维护者交流？**  
**A**：您可以通过 [关于我们](https://github.com/LLMQuant/Quant-Wiki) 页面找到联系方式，加入项目相关的讨论组，与其他开发者实时交流。

---

**Q：我要怎么贡献内容或代码？**  
**A**：请参考 [如何参与](https://github.com/LLMQuant/quant-wiki/blob/master/.github/CONTRIBUTING.md) 页面，里面有详细的说明，无论是贡献代码、完善文档，还是提出改进建议，都非常欢迎！

![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)

# 华泰人工智能研报精选

![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

## 系列简介

本系列收录了华泰人工智能相关的研究报告。

## 研报目录

### 华泰-人工智能系列

- [人工智能选股框架及经典算法简介](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列1：人工智能选股框架及经典算法简介.pdf)
- [人工智能选股广义线性模型](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列2：人工智能选股广义线性模型.pdf)
- [人工智能选股支持向量机模型](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列3：人工智能选股支持向量机模型.pdf)
- [人工智能选股朴素贝叶斯模型](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列4：人工智能选股朴素贝叶斯模型.pdf)
- [人工智能选股随机森林模型](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列5：人工智能选股随机森林模型.pdf)
- [人工智能选股Boosting模型](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列6：人工智能选股Boosting模型.pdf)
- [人工智能选股Python实战](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列7：人工智能选股Python实战.pdf)
- [人工智能选股全连接神经网络](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列8：人工智能选股全连接神经网络.pdf)
- [人工智能选股循环神经网络模型](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列9：人工智能选股循环神经网络模型.pdf)
- [宏观周期指标应用于随机森林选股](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列10：宏观周期指标应用于随机森林选股.pdf)
- [人工智能选股stacking集成学习](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列11：人工智能选股stacking集成学习.pdf)
- [人工智能选股特征选择](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列12：人工智能选股特征选择.pdf)
- [人工智能选股损失函数的改进](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列13：人工智能选股损失函数的改进.pdf)
- [从时序交叉验证谈起](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列14：对抗过拟合：从时序交叉验证谈起.pdf)
- [人工智能选股卷积神经网络](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列15：人工智能选股卷积神经网络.pdf)
- [再论时序交叉验证对抗过拟合](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列16：再论时序交叉验证对抗过拟合.pdf)
- [人工智能选股数据标注方法实证](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列17：人工智能选股数据标注方法实证.pdf)
- [机器学习选股模型的调仓频率实证](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列18：机器学习选股模型的调仓频率实证.pdf)
- [重采样技术检验过拟合](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列19：偶然中的必然：重采样技术检验过拟合.pdf)
- [机器学习中的随机数](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列20：必然中的偶然：机器学习中的随机数.pdf)
- [基于遗传规划的选股因子挖掘](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列21：基于遗传规划的选股因子挖掘.pdf)
- [基于CSCV框架的回测过拟合概率](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列22：基于CSCV框架的回测过拟合概率.pdf)
- [再探基于遗传规划的选股因子挖掘](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列23：再探基于遗传规划的选股因子挖掘.pdf)
- [技术分析可靠否？](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列24：真假序列识别研究-投石问路：技术分析可靠否？.pdf)
- [市场弱有效性检验与择时战场选择](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列25：真假序列识别再探：市场弱有效性检验与择时战场选择.pdf)
- [遗传规划在CTA信号挖掘中的应用](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列26：遗传规划在CTA信号挖掘中的应用.pdf)
- [揭开机器学习模型的"黑箱"](https://github.com/LLMQuant/asset/blob/main/%E5%8D%8E%E6%B3%B0%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD%E7%B3%BB%E5%88%9727%EF%BC%9A%E6%8F%AD%E5%BC%80%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%A8%A1%E5%9E%8B%E7%9A%84%E2%80%9C%E9%BB%91%E7%AE%B1%E2%80%9D.pdf)
- [基于量价的人工智能选股体系概览](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列28：基于量价的人工智能选股体系概览.pdf)
- [提升超额收益_另类标签和集成学习](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列29：提升超额收益_另类标签和集成学习.pdf)
- [因果推断初探](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列30：从关联到逻辑：因果推断初探.pdf)
- [生成对抗网络GAN初探](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列31：生成对抗网络GAN初探.pdf)
- [因子挖掘神经网络](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列32：AlphaNet：因子挖掘神经网络.pdf)
- [无监督学习案例](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列33：数据模式探索：无监督学习案例.pdf)
- [结构和特征优化](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列34：再探AlphaNet：结构和特征优化.pdf)
- [WGAN应用于金融时间序列生成](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列35：WGAN应用于金融时间序列生成.pdf)
- [相对生成对抗网络RGAN实证](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列36：相对生成对抗网络RGAN实证.pdf)
- [舆情因子和BERT情感分类模型](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列37：舆情因子和BERT情感分类模型.pdf)
- [从单资产到多资产](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列38：WGAN生成：从单资产到多资产.pdf)
- [周频量价选股模型的组合优化实证](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列39：周频量价选股模型的组合优化实证.pdf)
- [微软AI量化投资平台Qlib体验](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列40：微软AI量化投资平台Qlib体验.pdf)
- [基于BERT的分析师研报情感因子](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列41：基于BERT的分析师研报情感因子.pdf)
- [图神经网络选股与Qlib实践](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列42：图神经网络选股与Qlib实践.pdf)
- [因子观点融入机器学习](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列43：因子观点融入机器学习.pdf)
- [深度卷积GAN实证](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列44：深度卷积GAN实证.pdf)
- [cGAN应用于资产配置](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列45：cGAN应用于资产配置.pdf)
- [结构和损失函数](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列46：AlphaNet改进：结构和损失函数.pdf)
- [cGAN模拟宏观指标](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列47：cGAN模拟宏观指标.pdf)
- [cGAN应用于策略调参](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列48：对抗过拟合：cGAN应用于策略调参.pdf)
- [SinGAN单样本生成](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列49：SinGAN单样本生成.pdf)
- [再探cGAN资产配置](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列50：再探cGAN资产配置.pdf)
- [文本PEAD选股策略](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列51：文本PEAD选股策略.pdf)
- [神经网络组合优化初探](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列52：神经网络组合优化初探.pdf)
- [揭秘微软AI量化研究](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列53：揭秘微软AI量化研究.pdf)
- [基于遗传规划的一致预期因子挖掘](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列54：基于遗传规划的一致预期因子挖掘.pdf)
- [图神经网络选股的进阶路](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列55：图神经网络选股的进阶路.pdf)
- [新闻舆情分析的HAN网络选股](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列56：新闻舆情分析的HAN网络选股.pdf)
- [文本FADT选股](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列57：文本FADT选股.pdf)
- [分析师共同覆盖因子和图神经网络](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列58：分析师共同覆盖因子和图神经网络.pdf)
- [强化学习初探与DQN择时](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列59：强化学习初探与DQN择时.pdf)
- [有序回归](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列60：量化如何追求模糊的正确：有序回归.pdf)
- [深挖分析师共同覆盖中的关联因子](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列61：深挖分析师共同覆盖中的关联因子.pdf)
- [勾勒AI语义理解的轨迹](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列62：NLP综述：勾勒AI语义理解的轨迹.pdf)
- [再探文本FADT选股](https://github.com/LLMQuant/asset/blob/main/华泰人工智能系列63：再探文本FADT选股.pdf)

## 关于LLMQuant

LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
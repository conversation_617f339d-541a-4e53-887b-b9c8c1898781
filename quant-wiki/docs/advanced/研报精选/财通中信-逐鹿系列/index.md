![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)

# 财通中信逐鹿研报精选

![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

## 系列简介

本系列收录了财通中信逐鹿相关的研究报告。

## 研报目录

### 财通中信-逐鹿系列

- [AlphaZero，基于AutoML_Zero的](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告12：AlphaZero，基于AutoML_Zero的.pdf)
- [基于南向资金的选股择时策略](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告4：基于南向资金的选股择时策略.pdf)
- [基本面因子与量价因子融合模型](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告14：基本面因子与量价因子融合模型.pdf)
- [基于TiDE及其改进的因子融合模型](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告17：基于TiDE及其改进的因子融合模型.pdf)
- [基于领域知识生成的基本面因子挖掘框架](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告15：基于领域知识生成的基本面因子挖掘框架.pdf)
- [聪明的资金流向数据](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告3：聪明的资金流向数据.pdf)
- [杠杆因子和盈利收益因子的风格轮动](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告5：杠杆因子和盈利收益因子的风格轮动.pdf)
- [基于限价订单簿数据的](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告11：基于限价订单簿数据的.pdf)
- [光伏行业因子投资框架，如何构建光伏行业指数增强策略](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告10：光伏行业因子投资框架，如何构建光伏行业指数增强策略.pdf)
- [q-factor在A股实证及改进](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告2：q-factor在A股实证及改进.pdf)
- [北向机构持仓深入挖掘](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告8：北向机构持仓深入挖掘.pdf)
- [基于Graph Embedding的行业因子向量化](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告16：基于Graph Embedding的行业因子向量化.pdf)
- [基于超预期的事件驱动策略](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告7：基于超预期的事件驱动策略.pdf)
- [Model Zoo](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告18：Model Zoo.pdf)
- [基于QLIBALPHA360的Temporal_FusionTransformer选股模型](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告9：基于QLIBALPHA360的Temporal_FusionTransformer选股模型.pdf)
- [不同类型策略过去表现](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告巡礼：不同类型策略过去表现.pdf)
- [基于北向机构持仓的选股分析](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告6：基于北向机构持仓的选股分析.pdf)
- [基于openFE的](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告13：基于openFE的.pdf)
- [一致预期因子深度挖掘](https://github.com/LLMQuant/asset/blob/main/逐鹿Alpha专题报告1：一致预期因子深度挖掘.pdf)

## 关于LLMQuant

LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
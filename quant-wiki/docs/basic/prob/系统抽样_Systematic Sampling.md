![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 什么是系统抽样？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
系统抽样是一种概率抽样方法，其中从较大总体中选择样本成员时，会根据一个随机起点，但采用固定的周期性间隔。这个抽样间隔是通过将总体规模除以所需的样本规模来计算的。

### 关键要点

- 系统抽样是一种概率抽样方法，其中从较大总体中选择随机样本，并采用固定的周期性间隔。
- 这个固定的周期性间隔，被称为抽样间隔，是通过将总体规模除以所需的样本规模来计算的。
- 这种方法的优点包括消除了集群选择现象，以及数据污染的低概率。
- 缺点包括对特定模式的过度代表或代表不足，以及数据操纵的更大风险。
- 系统抽样主要有三种类型：随机系统抽样、线性系统抽样和循环系统抽样。

## 理解系统抽样

当在定义规模的庞大总体上正确执行时，系统抽样可以帮助研究人员（包括营销和销售专业人员）获得对庞大人群的代表性发现，而无需接触到每一个人。

由于对总体进行简单随机抽样可能效率低下且耗时，因此统计学家会转向其他方法，例如系统抽样。通过系统方法选择样本规模可以快速完成。一旦确定了一个固定的起点，就会选择一个恒定的间隔来方便参与者的选择。

当数据操纵的风险较低时，系统抽样优于简单随机抽样。如果研究人员可以操纵间隔长度以获得所需结果的风险很高，那么简单随机抽样技术将更合适。

系统抽样因其简单性而受到研究人员和分析师的欢迎。研究人员通常假设结果代表大多数正常总体，除非随机特征不成比例地存在于每个第 n 个数据样本中（这种情况不太可能发生）。换句话说，总体需要表现出一定程度的自然随机性以及所选指标。如果总体具有某种标准化模式，那么意外选择非常常见案例的风险会更加明显。

在系统抽样中，与其他抽样方法一样，必须在选择参与者之前选择目标总体。可以根据适合所进行研究目的的任意数量的所需特征来识别总体。一些选择标准可能包括年龄、性别、地点、教育程度或职业。

**重要提示：** 有几种对总体进行抽样以进行统计推断的方法。系统抽样是随机抽样的一种形式。

## 何时使用系统抽样

系统抽样最适合的一种情况是，当所研究的总体表现出一定程度的秩序或规律性时。例如，如果您正在对进入商店的顾客进行调查，系统抽样允许您系统地选择每第 n 位顾客，从而确保一天或一周中不同时间的代表性。这种方法有助于避免因仅选择在特定时间段到达的顾客而产生的偏差。

系统抽样适用的另一种情况是，当总体规模已知且相对较大时。系统抽样简化了流程，无需列出并从整个总体中随机选择个体，而是以设定的节奏选择样本。这在时间和资源有限的大规模研究中特别有用，这意味着您无需花费大量精力来规划样本。

当研究人员希望确保样本均匀分布在整个总体中时，可以使用系统抽样。例如，一家公司可以从按姓氏过滤的公司目录中选择每第 n 个人。其他形式的抽样可能会意外地聚集相似的群体（即，根据样本的聚合方式，从财务部门选择的人员过多）。

此外，与其他抽样方法相比，系统抽样具有简单易于实施的优点。它需要的计算量最少，并且可以使用简单的算法轻松执行，尤其是在目标样本规模和总人口规模已知的情况下。

## 创建系统样本的步骤

您可以使用以下步骤来创建系统样本：1

## 系统抽样示例

作为系统抽样的假设示例，假设在一个由 10,000 人组成的总体中，一位统计学家选择每 100 个人进行抽样。抽样间隔也可以是系统的，例如选择每 12 小时抽取一次新样本。

作为另一个例子，如果您想使用系统抽样从 50,000 人的总体中选择一个由 1,000 人组成的随机组，则必须将所有潜在参与者放在一个列表上，并选择一个起点。一旦形成列表，列表上的每 50 个人（从选定的起点开始计数）将被选为参与者，因为 50,000 ÷ 1,000 = 50。

例如，如果选定的起点是 20，则选择列表上的第 60 个人，然后选择第 120 个人，依此类推。一旦到达列表的末尾并且需要额外的参与者，计数将循环到列表的开头以完成计数。

**注意：** 要进行系统抽样，研究人员必须首先知道目标总体的大小。

## 系统抽样的类型

一般来说，有三种生成系统样本的方法：

这是系统抽样的经典形式，其中以预定的间隔选择主题。例如，如果研究人员想从 1000 名学生的总体中选择 100 名学生的样本，他们可以使用系统随机抽样，从按随机顺序排序的列表中选择每 10 名学生。这种方法确保了总体中的每个成员都有平等的机会被选中，同时仍然保持系统抽样模式。

与随机选择抽样间隔不同，这是指沿着线性路径创建跳过模式。这意味着，不是从总体中选择每第 n 个成员，而是选择过程遵循预定的顺序，例如选择每第 5 个成员，然后是每第 7 个成员，然后是每第 9 个成员，依此类推。在线性路径上存在特定顺序或序列的情况下，线性系统抽样可能很有用，例如沿线性路径的地理位置。

这是指样本在结束后再次从同一点开始。这意味着一旦抽样间隔到达总体的最后一个成员，它就会环绕到开头并继续选择过程。循环系统抽样通常用于总体表现出循环模式或没有明确的起点或终点的情况。例如，如果研究人员正在研究森林中的树木生长，他们可以使用循环系统抽样，通过沿圆形路径以规则的间隔选择树木，从而确保对森林区域的全面覆盖。

## 系统抽样与整群抽样

系统抽样和整群抽样在如何从样本中包含的总体中提取样本点方面有所不同。整群抽样将总体分解为群集，而系统抽样使用来自较大总体的固定间隔来创建样本。

系统抽样从总体中选择一个随机起点，然后根据总体的大小，从总体的规则固定间隔中抽取样本。整群抽样将总体划分为群集，然后从每个群集中抽取一个简单的随机样本。

整群抽样被认为不如其他抽样方法精确。但是，它可以节省获取样本的成本。整群抽样是一个两步抽样程序。当完成整个总体列表很困难时，可以使用它。例如，很难构建杂货店顾客的整个总体进行访谈。

但是，一个人可以创建一个随机的商店子集，这是该过程的第一步。第二步是采访这些商店顾客的随机样本。这是一个简单的手动过程，可以节省时间和金钱。

## 系统抽样时要避免的错误

使用系统抽样时，一个常见的陷阱是选择不合适的抽样间隔。选择过小的抽样间隔可能会导致过度抽样和抽样误差增加，而选择过大的间隔可能会导致抽样不足和样本代表性降低。可以通过在开始抽样之前充分了解总体的全部范围来避免此错误。

另一个要避免的错误是未能考虑抽样框架可能引入的潜在偏差。如果抽样框架不能代表感兴趣的总体，则系统抽样可能会导致有偏差的结果。例如，如果抽样框架仅包括来自某些人口群体或地理位置的个人，则样本将无法反映整个群体的多样性。这种类型的错误存在于所有形式的抽样中。

另一个需要注意的提示是考虑总体中是否存在系统模式或周期。如果总体中存在与抽样间隔对齐的周期性模式或趋势，则总体的某些部分可能会被系统地过度代表或代表不足。例如，想象一下从棒球名单中选择随机球员。如果这些名单按位置顺序列出，您最终可能会选择来自不同球队的相同位置的球员，因为总体具有循环模式。

## 系统抽样的局限性

统计学家在进行系统抽样时必须考虑的一个风险涉及与抽样间隔一起使用的列表的组织方式。如果放置在列表上的总体以与抽样间隔匹配的循环模式组织，则选定的样本可能会有偏差。

例如，一家公司的人力资源部门想要挑选一个员工样本，并询问他们对公司政策的看法。员工被分成 20 人的团队，每个团队由一名经理领导。如果用于选择样本大小的列表是按团队聚集在一起组织的，则统计学家可能会冒着仅选择经理（或根本没有经理）的风险，具体取决于抽样间隔。

## 如何执行系统抽样？

要进行系统抽样，首先，确定您要从中抽样的总人口规模。然后，选择一个随机起点，并根据预定的抽样间隔从总体中选择每第 n 个成员。

## 我应该在什么时候使用系统抽样？

当您需要一种简单有效的方法来从具有已知且均匀分布结构的大型总体中选择具有代表性的样本，并且当随机化对于您的研究目标不可行或不必要时，您应该使用系统抽样。

## 系统抽样的优点是什么？

系统抽样易于进行且易于理解，这就是为什么它通常受到研究人员的青睐。中心假设，即结果代表大多数正常总体，保证了对整个总体的均匀抽样。

此外，由于其过程，系统抽样与其他抽样方法相比提供了更高程度的控制。系统抽样也具有较低的风险因素，因为数据被污染的可能性很小。

## 系统抽样的缺点是什么？

系统抽样的主要缺点是需要总体规模。如果不了解总体中参与者的具体数量，系统抽样就无法很好地发挥作用。例如，如果一位统计学家想检查特定地区无家可归者的年龄，但无法准确获得有多少无家可归者，那么他们将没有人口规模或起点。另一个缺点是总体需要表现出一定量的自然随机性，否则选择相似实例的风险会增加，从而破坏了样本的目的。

## 整群抽样和系统抽样有何不同？

整群抽样和系统抽样在如何从样本中包含的总体中提取样本点方面有所不同。整群抽样将总体划分为群集，然后从每个群集中抽取一个简单的随机样本。系统抽样从总体中选择一个随机起点，然后根据总体的大小，从总体的规则固定间隔中抽取样本。整群抽样比系统抽样更容易出现较大的抽样误差，尽管它可能是一个更便宜的过程。

## 底线

抽样可能是得出关于一大群人、物品或其他感兴趣事物的结论的有效方法。系统抽样是实现此目的的最流行的方法之一，因为它比其他选项更便宜且更省时。是的，它并不完美。但是，如果您有一个大型数据集，并且间隔之间没有模式，那么系统抽样能够以相对较低的成本提供可靠的样本。

## 参考文献

[1] PennState, Eberly College of Science. "[STAT 506: Sampling Theory and Methods; 8.1 - Systematic Sampling](https://online.stat.psu.edu/stat506/lesson/8/8.1)."

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
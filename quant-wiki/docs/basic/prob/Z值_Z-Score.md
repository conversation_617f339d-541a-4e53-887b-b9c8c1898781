![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 什么是 Z 分数？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
Z 分数是一种统计测量方法，描述了一个值与一组值的平均值之间的关系。 Z 分数以与平均值的标准差来衡量。在投资和交易中，Z 分数是衡量工具波动性的指标，交易者可以使用它来帮助确定波动性。

### 主要收获

- Z 分数是对一组分数中某个分数与平均值关系的统计测量。
- Z 分数可以向交易者揭示某个值对于指定数据集是否典型，或者是否非典型。
- 一般来说，-3.0 到 3.0 的 Z 分数表明股票的交易价格在其平均值的三个标准差范围内。
- 交易者已经开发了许多使用 Z 分数来识别交易、交易头寸之间的相关性以及评估交易策略的方法。

## 理解 Z 分数

Z 分数是一种统计度量，用于量化数据点与数据集平均值之间的距离。它以标准差表示。它表明数据点与分布的平均值相差多少个标准差。

如果 Z 分数为 0，则表示数据点的分数与平均分数相同。 Z 分数为 1.0 表示该值与平均值相差一个标准差。 Z 分数可以是正数或负数，正值表示分数高于平均值，负值表示分数低于平均值。

Z 分数有时会与 Altman Z 分数混淆，后者是使用公司财务报告中的因素计算得出的。 Altman Z 分数用于计算企业在未来两年内破产的可能性，而 Z 分数可用于确定股票的回报与其平均回报的差异程度——以及更多。

**注意：** Z 分数也称为标准分数。

## Z 分数公式

值的 Z 分数的统计公式使用以下公式计算：

其中：

- z = Z 分数
- x = 被评估的值
- μ = 平均值
- σ = 标准差

## 如何计算 Z 分数

计算 Z 分数需要您首先确定数据的平均值和标准差。 获得这些数字后，您可以计算 Z 分数。 因此，假设您有以下变量：

- x = 57
- μ = 52
- σ = 4

您将在公式中使用这些变量：

- z = ( 57 - 52 ) / 4
- z = 1.25

因此，您选择的值的 Z 分数表明它与平均值相差 1.25 个标准差。

要使用电子表格确定 Z 分数，您需要输入您的值并确定范围的平均值和标准差。 使用以下公式：

您会发现以下值的平均值为 12.17，标准差为 6.4。

||A|B|C
|---|---|---|---|
|1|因子 (x)|平均值 (μ)|标准差 (σ)
|2|3|12.17|6.4
|3|13|12.17|6.4
|4|8|12.17|6.4
|5|21|12.17|6.4
|6|17|12.17|6.4
|7|11|12.17|6.4

使用 Z 分数公式，您可以计算出每个因子的 Z 分数。 在 D2 中使用以下公式，然后是 D3，依此类推：

||A|B|C|D
|---|---|---|---|---|
|1|因子 (x)|平均值 (μ)|标准差 (σ)|Z 分数
|2|3|12.17|6.4|-1.43
|3|13|12.17|6.4|0.13
|4|8|12.17|6.4|-0.65
|5|21|12.17|6.4|1.38
|6|17|12.17|6.4|0.75
|7|11|12.17|6.4|-0.18

## Z 分数如何使用

在其最基本的形式中，Z 分数允许您确定您正在评估的股票的回报与股票样本的平均值相差多远（以标准差衡量）。 您拥有的平均分数可以是股票年度回报的平均值、其上市指数的平均回报或您选择的股票选择的平均回报。

一些交易者在更高级的评估方法中使用 Z 分数，例如加权每只股票的回报以使用因子投资，其中使用 Z 分数和标准差根据特定属性评估股票。 在外汇市场中，交易者使用 Z 分数和置信限来测试交易系统产生盈利和亏损序列的能力。

## Z 分数与标准差

在大多数大型数据集中（假设数据呈正态分布），99.7% 的值位于 -3 和 3 个标准差之间，95% 位于 -2 和 2 个标准差之间，68% 位于 -1 和 1 个标准差之间。

标准差表示给定数据集内的变异量（或离散度）。 例如，如果一个正态分布的数据样本的标准差为 3.1，而另一个样本的标准差为 6.3，则标准差 (SD) 为 6.3 的模型更分散，并且绘制的峰值比 SD 为 3.1 的样本更低。

分布曲线有负面和正面，因此有正负标准差和 Z 分数。 但是，除了表明它位于平均值的哪一侧之外，这与值本身无关。 负值表示它位于平均值的左侧，正值表示它位于右侧。

Z 分数显示给定数据点与平均值相差的标准差数。 因此，必须首先计算标准差，因为 Z 分数使用它来传达数据点的变异性。

## 什么是 Z 分数？

Z 分数是一种确定数据与群体平均值之间的距离的方法，以标准差衡量。 它告诉我们数据点与群体的其余部分相比是典型的还是不寻常的，这对于发现不寻常的值和比较不同群体之间的数据很有用。

## 如何计算 Z 分数？

Z 分数的计算方法是找到数据点与数据集平均值之间的差值，然后将该差值除以标准差，以查看数据点与平均值相差多少个标准差。

## Z 分数在现实生活中如何使用？

Z 分数用于许多现实生活中的应用，例如医疗评估、测试评分、业务决策以及投资和交易机会衡量。 使用 Z 分数等统计指标来评估交易机会的交易者称为量化交易者（quantitative traders）。

## 什么是好的 Z 分数？

Z 分数越高（或越低），该点离平均值就越远。 这不一定是好事或坏事； 它只是显示数据在正态分布样本中的位置。 这意味着在评估投资或机会时，这取决于偏好。 例如，一些投资者使用 -3.0 到 3.0 的 Z 分数范围，因为 99.7% 的正态分布数据都落在此范围内，而另一些投资者可能使用 -1.5 到 1.5，因为他们更喜欢更接近平均值的分数。

## 为什么 Z 分数如此重要？

Z 分数很重要，因为它告诉您数据在数据分布中的位置。 例如，如果 Z 分数为 1.5，则它与平均值相差 1.5 个标准差。 因为 68% 的数据位于一个标准差内（如果它呈正态分布），所以 1.5 可能被认为离平均值太远，让您感到不舒服。

## 底线

Z 分数是一种统计测量方法，告诉您在正态分布样本中，您的数据与平均值（或平均值）相差多远。 在最基本的层面上，投资者和交易者使用诸如 Z 分数之类的定量分析方法来确定股票与其他股票或其自身历史表现相比的表现如何。 在更高级的 Z 分数使用中，交易者根据理想标准权衡投资、开发其他指标，甚至尝试预测交易策略的结果。

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 什么是 T 检验？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

T 检验是一种推论统计方法，用于确定两组数据的均值之间是否存在显著差异以及它们之间的关系。当数据集服从正态分布且具有未知方差时，例如从抛硬币 100 次记录的数据集，会使用 T 检验。

T 检验是统计学中用于假设检验的一种检验，它使用 t 统计量、t 分布值和自由度来确定统计显著性。

### 关键要点

- T 检验是一种推论统计方法，用于确定两个变量的均值之间是否存在统计上的显著差异。
- T 检验是统计学中用于假设检验的一种检验。
- 计算 T 检验需要三个基本数据值，包括每个数据集均值之间的差异、每组的标准差以及数据值的数量。
- T 检验可以是相关的或独立的。

## 理解 T 检验

T 检验比较两组数据集的平均值，并确定它们是否来自同一总体。在上面的例子中，来自 A 班的学生样本和来自 B 班的学生样本不太可能具有相同的均值和标准差。同样，从服用安慰剂的对照组和服用处方药的组中抽取的样本应该具有略微不同的均值和标准差。

在数学上，T 检验从两个集合中的每一个中抽取一个样本，并建立问题陈述。它假设一个零假设，即两个均值相等。

使用公式，计算值并与标准值进行比较。相应地接受或拒绝假设的零假设。如果零假设符合被拒绝的条件，则表明数据读数很强，并且可能不是由于偶然性造成的。

T 检验只是用于此目的的众多检验之一。统计学家使用 T 检验以外的其他检验来检查更多变量和更大的样本量。对于较大的样本量，统计学家使用 z 检验。其他测试选项包括卡方检验和 f 检验。

## 使用 T 检验

假设一家制药公司测试一种新药。按照标准程序，将药物给予一组患者，将安慰剂给予另一组患者，称为对照组。安慰剂是一种没有治疗价值的物质，作为衡量另一组（服用实际药物）反应的基准。

药物试验后，服用安慰剂的对照组的成员报告平均预期寿命增加了三年，而服用新药的组的成员报告平均预期寿命增加了四年。

初步观察表明该药物正在发挥作用。然而，这种观察结果也可能是由于偶然性造成的。可以使用 T 检验来确定结果是否正确并适用于整个群体。

使用 T 检验时会做出四个假设。收集的数据必须遵循连续或有序的尺度，例如智商测试的分数，数据是从总人口中随机选择的一部分收集的，数据将导致钟形曲线的正态分布，并且当标准差相等时存在相等或同质的方差。

## T 检验公式

计算 T 检验需要三个基本数据值。它们包括每个数据集的平均值之间的差异（即平均差异）、每组的标准差以及每组的数据值数量。

这种比较有助于确定偶然性对差异的影响，以及差异是否超出该偶然性范围。T 检验会质疑组之间的差异是否代表研究中的真实差异，或者仅仅是随机差异。

T 检验产生两个值作为其输出：t 值和自由度。t 值，或 t 分数，是两个样本集的均值之间的差异与样本集内存在的变异的比率。

分子值是两个样本集的均值之间的差异。分母是样本集中存在的变异，是对离散度或变异性的度量。

然后将计算出的 t 值与从称为 T 分布表的临界值表中获得的值进行比较。t 分数越高，表明两个样本集之间存在较大差异。t 值越小，两个样本集之间存在的相似性就越大。

**T 分数：** 较大的 t 分数（或 t 值）表明组之间存在差异，而较小的 t 分数表明组之间相似。

自由度是指研究中可以自由变化的值，对于评估零假设的重要性和有效性至关重要。这些值的计算通常取决于样本集中可用的数据记录的数量。

相关 t 检验或配对 t 检验是一种依赖型检验，当样本由相似单元的匹配对组成，或者存在重复测量的情况时执行。例如，可能存在在接受特定治疗之前和之后重复测试同一患者的情况。每个患者都被用作针对自身的对照样本。

此方法也适用于样本相关或具有匹配特征的情况，例如涉及儿童、父母或兄弟姐妹的比较分析。

用于计算配对 t 检验的 t 值和自由度的公式为：

$$ \begin{aligned}&T=\frac{\textit{mean}1 - \textit{mean}2}{\frac{s(\text{diff})}{\sqrt{(n)}}}\\&\textbf{其中：}\\&\textit{mean}1\text{ 和 }\textit{mean}2=\text{每个样本集的平均值}\\&s(\text{diff})=\text{配对数据值的差异的标准差}\\&n=\text{样本大小（配对差异的数量）}\\&n-1=\text{自由度}\end{aligned} $$

等方差 t 检验是一种独立的 t 检验，当每组中的样本数量相同，或者两个数据集的方差相似时使用。

用于计算等方差 t 检验的 t 值和自由度的公式为：

$$ \begin{aligned}&\text{T-value} = \frac{ mean1 - mean2 }{\frac {(n1 - 1) \times var1^2 + (n2 - 1) \times var2^2 }{ n1 +n2 - 2}\times \sqrt{ \frac{1}{n1} + \frac{1}{n2}} } \\&\textbf{其中：}\\&mean1 \text{ 和 } mean2 = \text{每个样本集的平均值} \\&var1 \text{ 和 } var2 = \text{每个样本集的方差} \\&n1 \text{ 和 } n2 = \text{每个样本集中的记录数} \end{aligned} $$

并且，

$$ \begin{aligned} &\text{自由度} = n1 + n2 - 2 \\ &\textbf{其中：}\\ &n1 \text{ 和 } n2 = \text{每个样本集中的记录数} \\ \end{aligned} $$

不等方差 t 检验是一种独立的 t 检验，当每组中的样本数量不同，并且两个数据集的方差也不同时使用。此检验也称为 Welch's t 检验。

用于计算不等方差 t 检验的 t 值和自由度的公式为：

$$ \begin{aligned}&\text{T-value}=\frac{mean1-mean2}{\sqrt{\bigg(\frac{var1}{n1}{+\frac{var2}{n2}\bigg)}}}\\&\textbf{其中：}\\&mean1 \text{ 和 } mean2 = \text{每个样本集的平均值} \\&var1 \text{ 和 } var2 = \text{每个样本集的方差} \\&n1 \text{ 和 } n2 = \text{每个样本集中的记录数} \end{aligned} $$

并且，

$$ \begin{aligned} &\text{自由度} = \frac{ \left ( \frac{ var1^2 }{ n1 } + \frac{ var2^2 }{ n2 } \right )^2 }{ \frac{ \left ( \frac{ var1^2 }{ n1 } \right )^2 }{ n1 - 1 } + \frac{ \left ( \frac{ var2^2 }{ n2 } \right )^2 }{ n2 - 1}} \\ &\textbf{其中：}\\ &var1 \text{ 和 } var2 = \text{每个样本集的方差} \\ &n1 \text{ 和 } n2 = \text{每个样本集中的记录数} \\ \end{aligned} $$

## 使用哪个 T 检验？

以下流程图可用于根据样本集的特征确定要使用的 T 检验。要考虑的关键项目包括样本记录的相似性、每个样本集中的数据记录的数量以及每个样本集的方差。

## 不等方差 T 检验的示例

假设对艺术画廊收到的画作进行对角线测量。一组样本包括 10 幅画作，而另一组包括 20 幅画作。数据集及其相应的均值和方差值如下：

||集合 1|集合 2
|---|---|---|
||19.7|28.3
||20.4|26.7
||19.6|20.1
||17.8|23.3
||18.5|25.2
||18.9|22.1
||18.3|17.7
||18.9|27.6
||19.5|20.6
||21.95|13.7
|||23.2
|||17.5
|||20.6
|||18
|||23.9
|||21.6
|||24.3
|||20.4
|||23.9
|||13.3
|平均值|19.4|21.6
|方差|1.4|17.1

虽然集合 2 的平均值高于集合 1 的平均值，但我们不能得出结论：与集合 1 对应的总体比与集合 1 对应的总体具有更高的平均值。

从 19.4 到 21.6 的差异仅仅是由于偶然性造成的，还是艺术画廊收到的所有画作的总体中存在差异？我们通过假设零假设（即两个样本集之间的平均值相同）来建立问题，并进行 T 检验以测试该假设是否合理。

由于数据记录的数量不同（n1 = 10 且 n2 = 20），并且方差也不同，因此使用不等方差 T 检验部分中提到的公式计算上述数据集的 t 值和自由度。

t 值为 -2.24787。由于在比较两个 t 值时可以忽略负号，因此计算值为 2.24787。

自由度值为 24.38，由于公式定义要求将该值向下舍入到最小可能的整数值，因此将其减少到 24。

可以指定概率水平（alpha 水平、显著性水平、p）作为接受的标准。在大多数情况下，可以假设 5% 的值。

使用自由度值 24 和 5% 的显著性水平，查看 t 值分布表得出值为 2.064。将此值与计算值 2.247 进行比较表明，计算出的 t 值大于显著性水平为 5% 的表值。因此，可以安全地拒绝均值之间没有差异的零假设。总体集具有内在差异，并且不是偶然造成的。

## 如何使用 T 分布表？

T 分布表有单尾和双尾格式。前者用于评估具有固定值或范围且具有明确方向（正或负）的情况。例如，输出值保持在 -3 以下的概率是多少，或者掷一对骰子时获得超过 7 的概率是多少？后者用于范围界限分析，例如询问坐标是否落在 -2 和 +2 之间。

## 什么是独立 T 检验？

独立 T 检验的样本是彼此独立选择的，其中两组中的数据集不引用相同的值。它们可能包括 100 名随机不相关的患者，分为两组，每组 50 名患者。其中一组成为对照组并服用安慰剂，而另一组接受处方治疗。这构成了两个独立的样本组，它们是不成对且彼此无关的。

## T 检验解释了什么以及如何使用它们？

T 检验是一种统计检验，用于比较两组的均值。它通常用于假设检验，以确定过程或治疗是否对感兴趣的总体产生影响，或者两组是否彼此不同。

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
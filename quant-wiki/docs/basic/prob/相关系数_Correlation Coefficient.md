![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 什么是相关系数？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
相关系数是一种衡量两个变量之间线性关系强度的统计量。它的取值范围在-1到1之间。相关系数为-1表示完全负相关或反相关，一个序列中的值上升时，另一个序列中的值下降，反之亦然。系数为1表示完全正相关或直接关系。相关系数为0表示没有线性关系。

相关系数在科学和金融领域中用于评估两个变量、因素或数据集之间的关联程度。例如，由于高油价对原油生产商有利，人们可能会认为油价与石油股票的远期回报之间的相关性是强正相关的。

然而，基于市场数据计算这些变量的相关系数表明，在较长时期内，相关性是中等且不一致的。[1]

### 关键要点

- 相关系数用于评估数据变量之间关联的强度。
- 最常见的是皮尔逊相关系数，它衡量两个变量之间线性关系的强度和方向。
- 取值范围始终为-1（完全反向或负相关关系）到1（完全正相关关系）。接近或等于零的值表示没有线性关系或相关性非常弱。
- 表明有意义关联所需的系数值取决于具体应用。相关性的统计显著性可以通过相关系数和样本中的数据点数量来计算，前提是假设总体呈正态分布。

## 理解相关系数

不同类型的相关系数用于根据比较数据的属性来评估相关性。到目前为止，最常见的是皮尔逊系数，或皮尔逊r，它衡量两个变量之间线性关系的强度和方向。皮尔逊系数无法评估变量之间的非线性关联，也无法区分因变量和自变量。

皮尔逊系数使用数学统计公式来衡量组合两个变量的数据点（一个数据序列的值绘制在x轴上，另一个序列的对应值绘制在y轴上）与最佳拟合线的接近程度。最佳拟合线可以通过回归分析确定。

系数离零越远，无论是正数还是负数，拟合效果越好，相关性越大。-1（负相关）和1（正相关）的值描述了完美拟合，其中所有数据点都排列在一条直线上，表明变量完全相关。

换句话说，这种关系是如此可预测，以至于可以从另一个变量的匹配值确定一个变量的值。相关系数越接近于零，相关性越弱，直到零时根本不存在线性关系。

基于相关系数值的相关强度评估因应用而异。在物理和化学中，相关系数应低于-0.9或高于0.9才能被认为是具有意义的，而在社会科学中，阈值可能高达-0.5，低至0.5。

对于从抽样获得的相关系数，统计显著性的确定取决于p值，该值是从数据样本的大小以及系数的值计算得出的。

## 相关系数的公式

要计算皮尔逊相关系数，首先要确定每个变量的标准差以及它们之间的协方差。相关系数是协方差除以两个变量标准差的乘积。

$$ \begin{aligned} &\rho_{xy} = \frac { \text{Cov} ( x, y ) }{ \sigma_x \sigma_y } \\ &\textbf{其中:} \\ &\rho_{xy} = \text{皮尔逊积矩相关系数} \\ &\text{Cov} ( x, y ) = \text{变量 } x \text{ 和 } y \text{ 的协方差} \\ &\sigma_x = \text{变量 } x \text{ 的标准差} \\ &\sigma_y = \text{变量 } y \text{ 的标准差} \\ \end{aligned} $$

标准差是衡量数据与其平均值离散程度的指标。协方差显示两个变量是否倾向于朝同一方向移动，而相关系数则在标准化尺度（从-1到1）上衡量这种关系的强度。

上面的公式可以进一步展开为

$$ \begin{aligned}&r = \frac { n \times ( \sum (X, Y) - ( \sum (X) \times \sum (Y) ) ) }{ \sqrt { ( n \times \sum (X ^ 2) - \sum (X) ^ 2 ) \times ( n \times \sum( Y ^ 2 ) - \sum (Y) ^ 2 ) } } \\&\textbf{其中:}\\&r=\text{相关系数}\\&n=\text{观测数量}\end{aligned} $$

## 相关统计和投资

相关系数在评估和管理投资风险方面特别有用。例如，现代投资组合理论认为，多元化可以降低投资组合回报的波动性，从而降低风险。相关性计算也是因子投资的主要内容，因子投资是一种基于与超额回报相关的因子构建投资组合的策略。同时，量化交易者使用历史相关性和相关系数来预测证券价格的近期变化。

## 皮尔逊相关系数的局限性

正如常言所说，相关性并不意味着因果关系，皮尔逊系数无法确定相关变量之一是否依赖于另一个变量。

相关系数也不能显示因变量的变化中有多少比例归因于自变量。这由决定系数表示，也称为R平方，它只是相关系数的平方。

相关系数不描述最佳拟合线的斜率；斜率可以通过回归分析中的最小二乘法确定。

皮尔逊相关系数不能用于评估非线性关联或由不符合正态分布的抽样数据产生的关联。它也可能被异常值扭曲——异常值是指远离分布散点图的数据点。

这些关系可以使用非参数方法进行分析，例如斯皮尔曼相关系数、肯德尔等级相关系数或多系列相关系数。

## 在 Excel 中查找相关系数

在 Excel 中计算相关性的最简单方法是在相邻列中输入两个数据系列，并使用内置的相关公式：

如果您想跨一系列数据集创建相关矩阵，Excel 在“数据”选项卡上的“分析”下有一个“数据分析”插件。

选择回报表。在这种情况下，我们的列是有标题的，因此我们要选中“首行标签”框，以便 Excel 知道将这些视为标题。然后您可以选择在同一张工作表或新工作表上输出。

点击回车将生成相关矩阵。您可以添加一些文本和条件格式来清理结果。

## R 和 R2 相同吗？

不，在分析系数时，R 和 R2 并不相同。 R 代表皮尔逊相关系数的值，用于表示变量之间的强度和方向，而 R2 代表决定系数，用于确定模型的强度。

## 如何计算相关系数？

相关系数的计算方法是确定变量的协方差，然后将该数字除以这些变量的标准差的乘积。

## 相关系数在投资中如何使用？

相关系数在投资组合风险评估和量化交易策略中起着关键作用。例如，一些投资组合经理会监控其持股的相关系数，以限制投资组合的波动性和风险。

## 结论

相关系数描述了一个变量如何相对于另一个变量移动。正相关表示两者朝同一方向移动，值为 1 表示完全正相关。值为 -1 表示完全负相关或反相关，而零表示不存在线性相关。

## 参考文献

[1] DataTrek Research. "[Oil Prices/Energy Stock Correlations, Rate Expectations](https://www.datatrekresearch.com/oil-prices-energy-stock-correlations-rate-expectations/)."
## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
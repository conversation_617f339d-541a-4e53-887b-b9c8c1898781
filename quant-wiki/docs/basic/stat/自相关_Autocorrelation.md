![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 自相关是什么？

![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
自相关是对给定时间序列与其自身延迟版本在连续时间区间内相似度的数学表示。它在概念上类似于两个不同时间序列之间的相关性，但自相关使用的是同一个时间序列两次：一次是其原始形式，另一次是延迟一个或多个时间周期后的形式。

例如，如果今天下雨，数据显示明天下雨的可能性比今天晴天时更高。在投资方面，一只股票的收益可能表现出强烈的正自相关，意味着如果今天“上涨”，明天也更有可能上涨。

自然，自相关可以成为交易者的一个有用工具，尤其是对技术分析师而言。

### 关键要点

- 自相关表示给定时间序列与其延迟版本在连续时间区间内的相似度。
- 自相关测量变量当前值与其过去值之间的关系。
- 自相关为 +1 表示完美的正相关，而 -1 则表示完美的负相关。
- 技术分析师可以利用自相关来衡量过去价格对证券未来价格的影响。

## 理解自相关

自相关也可以称为滞后相关或序列相关，因为它测量的是变量当前值与其过去值之间的关系。

以一个非常简单的例子来看，下面的表格展示了五个百分比值。我们将这些值与右侧的相同数据进行比较，该列中的数值只是向上移动了一行。

| 星期 | % 盈利或亏损 | 次日 % 盈利或亏损 |
|-----|-------------|------------------|
| 星期一 | 10%        | 5%               |
| 星期二 | 5%         | -2%              |
| 星期三 | -2%        | -8%              |
| 星期四 | -8%        | -5%              |
| 星期五 | -5%        |                   |

计算自相关的结果可以在 -1 到 +1 之间波动。

自相关为 +1 表示完美的正相关（在一个时间序列中观察到的增加，导致另一个时间序列中的相应增加）。

另一方面，自相关为 -1 表示完美的负相关（在一个时间序列中观察到的增加，导致另一个时间序列中的相应减少）。

自相关测量线性关系。即使自相关值微小，时间序列和其延迟版本之间仍可能存在非线性关系。

测试自相关最常用的方法是 Durbin-Watson 检验。简单来说，Durbin-Watson 是一种用于通过回归分析检测自相关的统计量。

Durbin-Watson 的测试结果范围总是从 0 到 4。值越接近 0 表示正相关的程度越大，越接近 4 则表示负自相关的程度越大，而接近中间的值则表明自相关较小。

**相关性与自相关的区别：** 相关性测量两个变量之间的关系，而自相关则测量一个变量与其延迟值之间的关系。

那么，自相关在金融市场中为何重要？原因很简单。自相关可以用于深度分析历史价格变动，投资者可以据此预测未来的价格变动。具体来说，自相关可以用来确定动量交易策略是否有意义。

## 自相关在技术分析中的应用

自相关对技术分析来说非常有用，因为技术分析主要关注证券价格的趋势及其之间的关系，采用图表技术。这与专注于公司财务健康或管理的基本面分析形成对比。

技术分析师可以利用自相关来评估过去证券价格对未来价格的影响。

自相关可以帮助判断某只股票是否存在动量因子。例如，如果一只具有高正自相关的股票连续两天大幅上涨，合理的预期是该股票在接下来的两天也会上涨。

## 自相关的例子

假设 Rain 想要确定其投资组合中的某只股票的收益是否表现出自相关；即该股票的收益与之前交易时段的收益相关。

如果收益表现出自相关，Rain 可以将其视为动量股，因为过去的收益似乎影响未来的收益。Rain 进行回归分析，以前一交易时段的收益作为自变量，当前收益作为因变量。他们发现前一天的收益具有 0.8 的正自相关。

由于 0.8 接近 +1，过去的收益似乎是该股票未来收益的一个非常好的正预测因子。

因此，Rain 可以调整其投资组合以利用自相关或动量，继续持有该股票或增加更多股份。

## 自相关与多重共线性的区别是什么？

自相关是变量值随时间变化的相关程度。而多重共线性则发生在自变量之间存在相关性，一个变量可以由另一个变量预测。自相关的例子包括测量某城市在 6 月 1 日的天气与同城市在 6 月 5 日的天气。而多重共线性则测量两个自变量之间的相关性，如一个人的身高与体重。

## 自相关为何会成为问题？

大多数统计检验假设观察值之间是独立的。换句话说，一个观察的发生并不能说明另一个观察的发生情况。自相关对于大多数统计检验来说是一个问题，因为它涉及到值之间缺乏独立性。

## 自相关的应用是什么？

自相关可以应用于许多学科，但通常见于技术分析。技术分析师评估证券，以识别趋势并预测其未来表现。

## 总结

自相关是一个时间序列与其延迟版本之间随时间变化的相关性。尽管与相关性相似，自相关使用的是同一时间序列两次。金融分析师和交易者利用自相关研究历史价格变动并预测未来走势。技术分析师利用自相关来确定历史证券价格对未来价格的影响。虽然自相关是一个非常有用的工具，但在金融分析中通常需要结合其他统计指标一起使用。

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
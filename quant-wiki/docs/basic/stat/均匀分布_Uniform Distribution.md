![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 什么是均匀分布？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
在统计学中，均匀分布是指一种概率分布，其中所有结果的发生概率相等。概率分布可以帮助你判断未来事件的可能性。

例如，抽取一副扑克牌时，抽到红桃、梅花、方块或黑桃的可能性是相等的，因此扑克牌的均匀分布是可以预期的。掷硬币也是一种均匀分布的例子，因为在掷硬币时，出现正面或反面的概率是相同的。

均匀分布可以视作一条水平直线。以掷硬币为例，出现正面或反面的概率各为50%（p = 0.50），在图表上绘制时，y轴的值为0.50。

### 关键要点

- 均匀分布是概率分布，其所有结果的发生可能性均等。
- 在离散均匀分布中，结果是离散的且具有相同的概率。
- 在连续均匀分布中，结果是连续的且是无限的。
- 在正态分布中，围绕均值的数据出现的频率高于离均值较远的数据。
- 均匀分布可以在图表上绘制。

## 理解均匀分布

均匀分布有两种类型：离散和连续。

掷骰子的结果提供了离散均匀分布的例子。可能的结果为1、2、3、4、5或6，但不可能掷出2.3、4.7或5.5。因此，骰子的掷出生成离散分布，每个结果的概率为1/6。只有6个可能的返回值，中间没有其他值，可能性是有限的。

相比之下，连续均匀分布则具有无限的分布可能性。理想化的随机数生成器就可被视为一种连续均匀分布。对于这种分布，0.0到1.0之间的每一点都有相同的出现机会，而这一区间内的点是无限的。

还有许多其他重要的连续分布，例如正态分布、卡方分布和学生t分布。

此外，还有几种与分布相关的数据生成或数据分析函数，这些函数帮助解释变量及其在数据集中的方差。这些函数包括概率密度函数、累积密度和矩生成函数。

**提示：** 统计学中的“离散”一词是指具有单一可计数且有限可能值的变量。

## 可视化均匀分布

分布是一种简单的方式来可视化一组数据。它可以以图表或列表的形式展示，显示随机变量的不同值发生的概率高低。均匀分布或许是所有概率分布中最简单的一种。

在均匀分布中，所有可能值的发生概率相同。当以条形图或折线图展示时，这种分布对每个可能结果的高度相同。这样，它的形状看起来像矩形，因此有时被称为矩形分布。

以抽取扑克牌中的某一花色为例，抽到红桃和抽到黑桃的随机机会是相同的，都是1/4或25%。

掷骰子会产生1、2、3、4、5或6中的一种数字。由于只有6种可能的结果，掷出其中任何一个的概率为16.67%（1/6）。在图表上绘制时，各个可能结果在x轴上固定，在y轴上用概率值表示。

## 均匀分布的例子

标准扑克牌共有52张，包含四种花色：红桃、方块、梅花和黑桃。每种花色均含有A、2、3、4、5、6、7、8、9、10、J、Q、K。同时，该套牌也有2张小丑，但在此例中我们将忽略小丑和人头牌，只关注每种花色中的数字牌。因此，我们剩下40张牌，形成一组离散数据。

假设你想知道从该修改后的牌中抽到红桃2的概率，抽到红桃2的概率为1/40或2.5%。每张牌都是独一无二的，因此从牌堆中抽到任何一张的可能性是相同的。

现在，让我们考虑从牌堆中抽到红桃的可能性。这个概率显著更高。为什么？因为我们现在只关注牌堆中的花色。由于只有四种花色，抽到红桃的概率为1/4或25%。

**注意：** 掷骰子的结果绘制将是离散均匀的，而多次掷骰子（多个骰子）的结果（均值）将是正态分布的。

## 均匀分布与正态分布

一些最常见的概率分布包括：

- 离散均匀分布
- 二项分布
- 连续均匀分布
- 正态分布
- 指数分布

或许，人们最熟悉且广泛使用的是正态分布，通常表现为钟形曲线。正态分布展示了连续数据的分布，并将大部分数据集中于均值或平均值附近。

在正态分布中，曲线下的面积等于1，68.27%的数据落在均值1个标准差（数据分散的程度）之内；95.45%的数据落在均值2个标准差之内，约99.73%的数据落在均值3个标准差之内。随着数据点远离均值，发生的频率减少。

离散均匀分布表明某个范围内的变量具有相同的发生概率。它的可能结果没有变异，数据是离散的，而不是连续的。形状类似矩形，而非正态分布的钟形。尽管如此，图形下的面积仍然等于1。

## 什么是均匀分布？

均匀分布是一种概率分布，表明离散数据集中的结果可能性对于每个值而言都是相同的。

## 均匀分布的公式是什么？

离散均匀分布的公式是：
$$ \begin{aligned}&P_x = \frac{ 1 }{ n } \\&\textbf{其中：} \\&P_x = \text{离散值的概率} \\&n = \text{范围内的值的数量} \\\end{aligned} $$

## 均匀分布是正态的吗？

否，均匀分布不是正态的。正态分布指数据围绕均值的分布情况，说明变量在均值附近发生的概率高于远离均值发生的概率。正态数据的发生概率不是均匀的，而均匀分布的发生概率是一致的。

## 均匀分布的期望是什么？

均匀分布的期望是所有可能结果拥有相同的概率。一个变量的概率与另一个变量的概率相同。

## 总结

均匀分布是统计学中多种概率分布的示例之一。均匀分布是所有可能结果都是有限的且发生概率相等的分布。例如，掷一个骰子只会呈现六种可能的结果，每一种结果发生的概率都是⅙。

## 参考文献

[1] National Institute of Standards and Technology. "[What Do We Mean by "Normal" Data?](https://www.itl.nist.gov/div898/handbook/pmc/section5/pmc51.htm)"

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
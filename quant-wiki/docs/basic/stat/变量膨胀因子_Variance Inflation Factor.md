![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 变量膨胀因子（VIF）是什么？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
变量膨胀因子（VIF）是回归分析中衡量多重共线性程度的一种指标。当多个自变量在多元回归模型中存在相关性时，便会产生多重共线性，这会对回归结果产生不利影响。因此，变量膨胀因子能够估计由于多重共线性导致的回归系数方差的膨胀程度。

### 关键要点

- 变量膨胀因子（VIF）提供了多元回归模型中自变量之间多重共线性的度量。
- 识别多重共线性非常重要，因为虽然多重共线性不会降低模型的解释能力，但会降低自变量的统计显著性。
- 自变量的VIF值较大，表明其与其他变量之间存在高度共线性关系，这需要在模型结构和自变量选择中予以考虑或调整。

## 理解变量膨胀因子（VIF）

变量膨胀因子是识别多重共线性程度的工具。当研究者希望测试多个变量对特定结果的影响时，会使用多元回归。因变量是受到自变量影响的结果。而多重共线性在于一个或多个自变量之间存在线性关系或相关性。

多重共线性在多元回归模型中造成问题，因为自变量之间相互影响，从而实际上并非独立，这使得在回归模型中测试自变量组合如何影响因变量变得困难。

尽管多重共线性不降低模型的整体预测能力，但可能会导致回归系数的估计值不具统计显著性。在某种意义上，这可以被视为模型中一种重复计数的现象。

在统计学中，存在高多重共线性的多元回归模型会使估计出每个自变量与因变量之间的关系变得更加困难。换句话说，当两个或多个自变量紧密相关或几乎测量相同内容时，它们所测量的潜在效应就会在变量之间被重复计算。当自变量之间高度相关时，很难确定哪一个变量在影响因变量。

数据的小幅变化或模型方程结构的调整可能会导致自变量估计系数的巨大和不稳定变化。这是一个问题，因为许多计量经济模型的目标正是准确测试自变量与因变量之间的这种统计关系。

为了确保模型的适当指定和正常运作，可以进行多重共线性测试。变量膨胀因子就是这样的一种衡量工具。利用变量膨胀因子有助于识别任何多重共线性问题的严重性，以便进行模型调整。变量膨胀因子衡量了自变量的行为（方差）受到其他自变量相互作用或相关性的影响程度。

变量膨胀因子为评估回归中变量对标准误差的贡献提供了快速的度量。当存在显著的多重共线性问题时，相关变量的变量膨胀因子将非常大。确定这些变量后，可以使用多种方法来消除或合并共线性变量，以解决多重共线性问题。

## VIF的公式与计算

VIF的公式为：

$$ \begin{aligned}&\text{VIF}_i = \frac{ 1 }{ 1 - R_i^2 } \\&\textbf{其中:} \\&R_i^2 = \text{在剩余的自变量上回归第} i \text{个自变量的未调整决定系数} \\\end{aligned} $$

## VIF可以告诉你什么？

当 $R_i^2$ 等于 0 时，VIF 或容忍度即为 1，这意味着第 $i$ 个自变量与其余自变量不相关，表明不存在多重共线性。

一般而言：

- VIF 等于 1 = 自变量不相关
- VIF 在 1 和 5 之间 = 自变量中度相关
- VIF 大于 5 = 自变量高度相关

VIF 越高，表明越有可能存在多重共线性，需要进一步研究。当 VIF 超过 10 时，存在需要纠正的显著多重共线性。

## 使用 VIF 的示例

例如，假设一位经济学家希望测试失业率（自变量）与通货膨胀率（因变量）之间是否存在统计显著关系。如果将与失业率相关的其他自变量，例如新增初次失业救济申请，纳入模型，可能会引入多重共线性。

整体模型可能显示出强大的统计解释能力，但无法确定影响主要是由于失业率还是新增初次失业救济申请。这便是 VIF 能检测到的问题，它可能建议将其中一个变量从模型中剔除，或寻找某种方式合并它们，以捕获其联合效应，具体取决于研究者想要检验的假设。

## VIF 的良好值是多少？

作为经验法则，VIF 值在 3 以下不会引起担忧。随着 VIF 的增加，你的回归结果的可靠性将降低。

## VIF 等于 1 代表什么？

VIF 等于 1 意味着自变量不相关，回归模型中不存在多重共线性。

## VIF 用于什么？

VIF 用于衡量回归分析中自变量之间的相关性强度。这种相关性被称为多重共线性，可能对回归模型造成问题。

## 总结

虽然回归模型中适度的多重共线性是可以接受的，但较高的多重共线性则可能引发关注。

可以采取两种措施来纠正高多重共线性。首先，可以剔除一个或多个高度相关的变量，因为这些变量提供的信息是冗余的。第二种方法是使用主成分分析或偏最小二乘回归，而非普通最小二乘回归，这样可以分别将变量减少为无相关性的较小集合，或创建新的无相关变量。这将提升模型的可预测性。

## 参考文献

[1] CFI. "[Variance Inflation Factor](https://corporatefinanceinstitute.com/resources/knowledge/other/variance-inflation-factor-vif/)."

[2] Isixsigma. "[Variance Inflation Factor (VIF)](https://www.isixsigma.com/dictionary/variance-inflation-factor-vif/)."

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
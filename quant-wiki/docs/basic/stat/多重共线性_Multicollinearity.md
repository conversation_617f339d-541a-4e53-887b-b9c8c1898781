![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 什么是多重共线性？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
多重共线性是指在多元回归模型中，两个或更多自变量之间存在高度相关性。当研究人员或分析师试图确定每个自变量在统计模型中预测或理解因变量的效果时，多重共线性可能导致结果扭曲或误导。

总体而言，多重共线性会导致置信区间变宽，从而产生不那么可靠的自变量影响概率。

在技术分析中，多重共线性可能导致关于投资的错误假设。通常，当使用多个相同类型的指标分析一只股票时，就会出现这种情况。

### 关键要点

- 多重共线性是一种统计概念，指模型中多个自变量之间存在相关性。
- 如果两个变量的相关系数为+/- 1.0，则认为它们是完全共线的。
- 自变量之间的多重共线性将导致统计推断的可靠性下降。
- 在分析投资时，最好使用不同类型的指标，而不是多个相同类型的指标，以避免多重共线性。
- 多重共线性可能导致结果的可靠性下降，因为你比较的结果通常是相似的。

## 理解多重共线性

统计分析师使用多元回归模型来预测某个特定因变量的值，这个因变量是基于两个或更多自变量的值。因变量有时被称为结果、目标或标准变量。

举个例子，一个多元回归模型试图根据市盈率（P/E比率）、市值或其他数据预测股票回报。股票回报是因变量（结果），而各种财务数据则是自变量。

在多元回归模型中的多重共线性表明，共线的自变量并不真正独立。例如，过去的表现可能与市值相关。业绩良好的公司股票通常伴随投资者信心上升，导致对该公司的股票需求增加，从而提升其市场价值。

虽然多重共线性不影响回归估计，但它使得这些估计模糊、不精确且不可靠。因此，难以确定自变量对因变量的单独影响。这会抬高一些或所有回归系数的标准误差。

一种称为方差膨胀因子（VIF）的统计技术可以检测和度量多元回归模型中的共线性。VIF测量估计回归系数的方差与自变量不呈线性关系时的方差相比膨胀的程度。VIF为1意味着变量之间不相关；1到5之间的VIF表示变量之间中度相关；而5到10的VIF则表示变量之间高度相关。

在分析股票时，你可以通过观察指标是否绘制相同图形来检测多重共线性。例如，在交易图表上选择两个动量指标，通常会生成显示相同动量的趋势线。

## 多重共线性的原因

当两个自变量高度相关时，可能会出现多重共线性。若某个自变量是从数据集中的其他变量计算得出的，或是两个自变量提供类似且重复的结果，也会导致多重共线性。

如果你使用相同的数据创建两个或三个相同类型的交易指标，结果将会是多重共线性的，因为这些数据及其被操纵的方式非常相似。

**重要提示：** 包含多重共线性的模型所产生的统计推断可能不可靠。

## 多重共线性的类型

完全共线性指多个自变量之间存在精确的线性关系，通常在图表中可以看到数据点完全落在回归线上。技术分析中，当同时使用两个测量同一指标的指标（例如成交量）时，也会出现这种情况。如果将一个指标叠加在另一个上，二者将毫无区别。

高共线性指多个自变量之间存在一定的相关性，但这种相关性并不像完全共线性那么紧密。并非所有数据点都落在回归线上，但依旧表明数据之间的相关性过于紧密，不宜使用。

在技术分析中，高共线性的指标通常会产生非常相似的结果。

结构性共线性发生在你使用数据创建新特征时。例如，如果你收集了数据并用于其他计算，再对结果进行回归分析，这些结果将会相关，因为它们互相衍生。

这是在投资分析中常见的共线性类型，因为相同的数据用来创建不同的指标。

实验设计不当或数据收集过程，例如使用观察性数据，通常会导致基于数据的共线性，在这种情况下，某些或所有变量都是相关的。

用于创建指标的股票数据通常是从历史价格和交易量中收集的，因此，由于数据收集方式不当而导致的多重共线性的可能性较小。

## 投资中的多重共线性

在投资时，多重共线性是一项常见考虑因素，特别是在进行技术分析以预测某个证券（如股票或商品期货）未来价格走势时。

市场分析师希望避免使用基于非常相似或相关输入的共线性技术指标；这些输入并不是指数据本身，而是数据如何被操纵以实现结果。

因此，分析必须基于显著不同的指标，以确保从独立的分析角度分析市场。例如，动量指标和趋势指标共享相同的数据，但它们不会完全共线，甚至可能没有高度共线性。这两个指标根据数据的操纵方式产生不同的结果。

**注意：** 大多数投资者不会过于关注指标计算背后的数据和技术——了解多重共线性是什么以及它如何影响分析就足够了。

## 如何解决多重共线性

消除多重共线性问题的常见方法之一是首先识别出共线的自变量，然后去除一个或多个自变量。通常，在统计学中，会进行方差膨胀因子的计算，以确定多重共线性的程度。解决多重共线性的另一种方法是在不同条件下收集更多数据。

著名技术分析师约翰·博林格（John Bollinger），博林格带指标的创始人，曾写道：“成功使用技术分析的基本规则要求避免指标之间的多重共线性。”为了解决这个问题，分析师避免使用两种或多种相同类型的技术指标。相反，他们使用一种类型的指标（如动量指标）分析某个证券，然后再使用另一种类型的指标（如趋势指标）进行单独分析。

例如，随机指标、相对强弱指标（RSI）和威廉指标（Wm%R）都是依赖相似输入的动量指标，可能会产生类似的结果。在上述图片中，随机指标和威廉指标是相同的，因此同时使用它们并没有揭示太多。在这种情况下，最好去掉其中一个指标，使用一个不跟踪动量的指标。在下面的图像中，随机指标显示价格动量，而博林格带宽显示价格在价格变动前的整理。

## 如何处理多重共线性？

为了减少统计模型中的多重共线性，可以去除被识别为最共线的特定变量。你也可以尝试组合或转化问题变量以降低它们的相关性。如果这仍然不可行，还有改进过的回归模型更好地处理多重共线性，例如岭回归、主成分回归或偏最小二乘回归。在股票分析中，最佳方法是选择不同类型的指标。

## 回归中的多重共线性是什么？

多重共线性描述了变量之间的关系，导致它们互相关联。多重共线性数据给分析带来问题，因为它们不是独立的。

## 如何解读多重共线性结果？

当变量膨胀因子超过五时，数据将表现出高度多重共线性。如果VIF在1到5之间，变量之间是中度相关的，而等于1时则表示不相关。在技术分析中，指标通常是相同的。

## 什么是完美共线性？

完美共线性存在于模型中两个自变量之间有精确的1:1对应关系时。相关系数可以是+1.0或-1.0。

## 为什么多重共线性是个问题？

多重共线性是个问题，因为它产生的回归模型结果不够可靠。这是由于置信区间变宽（标准误差增大）可能降低回归系数的统计显著性。在股票分析中，它可能导致对投资的错误印象或假设。

## 总结

多重共线性存在于自变量与多个其他自变量高度相关的多元回归方程中。多重共线性是个问题，因为它使得统计推断不够可靠。然而，方差膨胀因子（VIF）可以提供有关哪些变量是冗余的信息，因此可以去除VIF高的变量。

在使用技术分析时，多重共线性成为问题，因为许多指标以相同的方式展示数据。为了避免这种情况，最好使用不测量相同趋势的指标。

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
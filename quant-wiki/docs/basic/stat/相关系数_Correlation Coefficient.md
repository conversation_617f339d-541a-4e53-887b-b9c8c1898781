![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 相关系数是什么？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
相关系数是衡量两个变量之间线性关系强度的统计指标。其值的范围从 -1 到 1。相关系数为 -1 表示存在完美的负相关，意味着一个系列中的值上升时，另一个系列中的值则下降，反之亦然。而相关系数为 1 表示存在完美的正相关，或直接关系。相关系数为 0 则表示没有线性关系。

相关系数在科学和金融领域被广泛使用，以评估两个变量、因素或数据集之间的关联程度。例如，由于高油价对原油生产商有利，因此可以假设油价与石油股票的未来收益之间的相关性是高度正相关的。然而，根据市场数据计算的这些变量的相关系数却显示出在较长时期内存在中等且不一致的相关性。

### 关键要点

- 相关系数用于评估数据变量之间的关联强度。
- 最常见的相关系数被称为“皮尔逊相关系数”，用于衡量两个变量之间线性关系的强度和方向。
- 数值总是介于 -1（完美的负相关）到 1（完美的正相关）之间。
- 靠近零的值表明没有线性关系或非常弱的相关性。
- 确定有意义的关联所需的系数值因应用而异。相关性的统计显著性可以通过相关系数和样本中数据点的数量来计算，假设样本符合正态分布。

## 理解相关系数

不同类型的相关系数用于根据比较数据的特性评估相关性。其中，最常用的是皮尔逊系数，又称为“皮尔逊 R”，用于测量两个变量之间线性关系的强度和方向。

皮尔逊系数通过一个数学统计公式，测量结合两个变量的数据点（将一个数据系列的值绘制在 x 轴上，对应的另一个系列的值绘制在 y 轴上）与最佳拟合线的接近程度。最佳拟合线可以通过回归分析确定。

**重要提示：** 最常见的皮尔逊系数无法评估变量之间的非线性关联，也不能区分因变量和自变量。

系数距离零越远，无论是正值还是负值，拟合程度越好，相关性越强。-1（负相关）和 1（正相关）的值描述了完美的拟合，所有数据点沿着一条直线排列，表明变量之间是完美相关的。

换句话说，这种关系是如此可预测，以至于可以从一个变量的值确定另一个变量的匹配值。相关系数越接近零，相关性越弱，直到零表示完全没有线性关系。

基于相关系数值的相关强度评估在不同应用中有所不同。在物理和化学领域，相关系数需要低于 -0.9 或高于 0.9 才能被认为是有意义的，而在社会科学中，这一阈值可能仅为 -0.5 或 0.5。

对于从抽样得出的相关系数，统计显著性的判断依赖于 p 值，这个值是基于数据样本的大小以及系数的值计算得出的。

## 相关系数的公式

为了计算皮尔逊相关系数，首先需要确定每个变量的标准差以及它们之间的协方差。相关系数为协方差除以这两个变量标准差的乘积。

$$ \begin{aligned} &\rho_{xy} = \frac { \text{Cov} ( x, y ) }{ \sigma_x \sigma_y } \\ &\textbf{其中：} \\ &\rho_{xy} = \text{皮尔逊积矩相关系数} \\ &\text{Cov} ( x, y ) = \text{变量 } x \text{ 和 } y \text{ 的协方差} \\ &\sigma_x = \text{变量 } x \text{ 的标准差} \\ &\sigma_y = \text{变量 } y \text{ 的标准差} \\ \end{aligned} $$

标准差是测量数据从其平均值的离散程度。协方差显示两个变量是否倾向于朝相同方向移动，而相关系数在归一化的尺度上（-1 到 1）测量这种关系的强度。

上述公式可以进一步详细化为：

$$ \begin{aligned}&r = \frac { n \times ( \sum (X, Y) - ( \sum (X) \times \sum (Y) ) ) }{ \sqrt { ( n \times \sum (X ^ 2) - \sum (X) ^ 2 ) \times ( n \times \sum( Y ^ 2 ) - \sum (Y) ^ 2 ) } } \\&\textbf{其中：}\\&r=\text{相关系数}\\&n=\text{观察数}\end{aligned} $$

## 相关统计与投资

相关系数在评估和管理投资风险方面特别有用。例如，现代投资组合理论认为，多样化可以降低投资组合回报的波动性，从而降低风险。相关历史回报之间的相关系数可以指示添加某项投资到投资组合中是否会改善其多样化程度。

相关计算也是因子投资的基础，因子投资是一种基于与超额回报相关因素构建投资组合的策略。同时，量化交易者利用历史相关性和相关系数来预测证券价格的短期变化。

## 皮尔逊相关系数的局限性

正如俗话所说，相关性并不意味着因果关系，而皮尔逊系数无法确定相关变量之间是否存在依赖关系。

此外，相关系数也无法显示因变量中有多少变化可归因于自变量。这由决定系数（也称为“R平方”）显示，该值仅是相关系数的平方。

相关系数也不描述最佳拟合线的斜率；斜率可以通过回归分析中的最小二乘法来确定。

皮尔逊相关系数不能用于评估非线性关联或源于不符合正态分布的抽样数据的关联。它还可能受到异常值的扭曲——即远离分布的散点图的数据点。

这些关系可以通过非参数方法进行分析，例如斯皮尔曼相关系数、肯德尔秩相关系数或多重相关系数。

## 在Excel中查找相关系数

在Excel中有几种计算相关性的方式。最简单的方法是将两个数据系列输入到相邻的列中，并使用内置的相关性公式：

如果您想在多个数据集之间创建相关矩阵，Excel提供了数据分析插件。使用该插件前，您需要先启用数据分析工具包。方法是在“文件”中点击“选项”，这将打开Excel选项对话框。在该框中，点击“加载项”，然后在“管理”下拉菜单中选择“Excel加载项”，并点击“转到”。这将出现加载项框。勾选“分析工具库”复选框，然后点击“确定”。这样，启用过程就完成了。

使用数据分析插件时，点击“数据”选项卡，然后选择“数据分析”，这将打开一个框。在框中，点击“相关性”，然后点击“确定”。相关性框现在会打开，您可以手动输入输入范围或选择相关单元格。

在这种情况下，由于我们的列有标题，因此我们需要勾选“第一行包含标签”框，以便Excel知道将其视为标题。然后，您可以选择在同一工作表或新工作表上输出结果。

按下 Enter 键将生成相关矩阵。您可以添加一些文本和条件格式以整理结果。

## R 和 R2 是一样的吗？

不，R 和 R2 在分析系数时并不相同。R 代表皮尔逊相关系数的值，用于表示变量之间的强度和方向，而 R2 代表决定系数，用于确定模型的强度。

## 如何计算相关系数？

相关系数通过确定变量的协方差，并将该数字除以这些变量标准差的乘积来计算。

## 相关系数在投资中的作用是什么？

相关系数在投资组合风险评估和量化交易策略中发挥着关键作用。例如，一些投资组合经理会监控其持有资产的相关系数，以限制投资组合的波动性和风险。

## 总结

相关系数描述一个变量如何相对于另一个变量变动。正相关表明两个变量朝同一方向移动，值为 1 表示完美的正相关。值为 -1 则表示完美的负相关，而 0 表示不存在线性相关性。

## 参考文献

[1] DataTrek Research. "[Oil Prices/Energy Stock Correlations, Rate Expectations](https://www.datatrekresearch.com/oil-prices-energy-stock-correlations-rate-expectations/)."
## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
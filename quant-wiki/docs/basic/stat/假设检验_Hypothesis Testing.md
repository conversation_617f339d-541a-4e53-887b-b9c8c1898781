![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 什么是假设检验？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
假设检验，有时称为显著性检验，是统计学中的一种行为，分析师通过它检验有关总体参数的假设。分析师采用的方法取决于所用数据的性质和分析的目的。

假设检验用于通过样本数据评估某一假设的合理性。这些数据可能来自更大的总体或数据生成过程。在以下描述中，“总体”一词将用于这两种情况。

### 关键要点

- 假设检验用于通过样本数据评估某一假设的合理性。
- 检验提供了有关假设合理性的证据。
- 统计分析师通过测量和检查所分析总体的随机样本来检验假设。
- 假设检验的四个步骤包括陈述假设、制定分析计划、分析样本数据和分析结果。

## 假设检验的工作原理

在假设检验中，分析师测试一个统计样本，旨在提供关于零假设合理性的证据。统计分析师测量并检查所分析总体的随机样本。所有分析师都使用随机总体样本来检验两个不同的假设：零假设和替代假设。

零假设通常是总体参数之间的相等假设；例如，零假设可能声明总体平均收益等于零。替代假设实际上是零假设的反面。因此，它们是相互排斥的，只有一个可以为真。然而，这两个假设中总有一个是正确的。

**重要提示：** 零假设是关于总体参数的声明，例如总体平均数，被假定为真实的。

## 四步过程

## 假设检验的例子

假设某人想要检验一枚硬币正面朝上的概率是否确实为50%，则零假设为50%是正确的，替代假设为50%不正确。从数学上讲，零假设表示为 Ho: P = 0.5。替代假设表示为“Ha”，与零假设相同，只是等号被划去，意味着它不等于50%。

随机抽取100次掷币的样本进行检验。如果发现100次掷币的结果为40次正面和60次反面，分析师将假定该硬币的正面概率并非50%，因此拒绝零假设，接受替代假设。

如果结果是48次正面和52次反面，那么该硬币可能是公平的，仍然可以产生这样的结果。在这种情况下，当零假设被“接受”时，分析师会指出，预期结果（50次正面和50次反面）与观察结果（48次正面和52次反面）之间的差异是“仅可以由随机因素解释的”。

## 假设检验何时开始？

一些统计学家将首个假设检验归功于1710年的讽刺作家约翰·阿巴特诺特（John Arbuthnot），他在观察到几乎每年男性出生人数略多于女性后，研究了英国的男性和女性出生情况。阿巴特诺特计算出这一现象偶然发生的概率很小，因此认为这是一种“神的庇佑”。

## 假设检验的好处是什么？

假设检验通过将新想法或理论与数据进行对比，帮助评估其准确性。这使得研究人员能够确定证据是否支持他们的假设，从而避免错误的主张和结论。假设检验还为基于数据而非个人意见或偏见的决策提供了框架。依靠统计分析，假设检验有助于减少随机因素和混杂变量的影响，为做出明智的结论提供了稳健的框架。

## 假设检验的局限性是什么？

假设检验完全依赖于数据，并不提供对所研究主题的全面理解。此外，结果的准确性取决于可用数据的质量和使用的统计方法。不准确的数据或不恰当的假设制定可能导致错误的结论或检验失败。假设检验还可能导致错误，例如分析师在不应该接受或拒绝零假设时发生这些错误。这些错误可能导致错误的结论或未能识别数据中的显著模式或关系。

## 结论

假设检验是一个统计过程，帮助研究人员确定研究的可靠性。通过使用合理制定的假设和一系列统计检验，个人或企业能够对所研究的总体进行推断，并根据呈现的数据得出结论。所有假设检验方法都有相同的四步过程，其中包括陈述假设、制定分析计划、分析样本数据和分析结果。

## 参考文献

[1] Sage. "[Introduction to Hypothesis Testing](https://www.sagepub.com/sites/default/files/upm-binaries/40007_Chapter8.pdf)," Page 4.

[2] Elder Research. "[Who Invented the Null Hypothesis?](https://www.elderresearch.com/blog/who-invented-the-null-hypothesis/)"

[3] Formplus. "[Hypothesis Testing: Definition, Uses, Limitations and Examples](https://www.formpl.us/blog/hypothesis-testing#:~:text=The%20most%20significant%20benefit%20of,%E2%80%9Cis%20or%20is%20not%E2%80%9D.)."

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 何为决定系数？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
决定系数是一个统计学测量，用于检视一个变量的差异如何能被另一个变量的差异解释，特别是在预测特定事件结果时。这个系数更常被称为 r-squared（或 r²）。它评估了两个变量之间线性关系的强度，并在投资者进行趋势分析时被广泛依赖。

这个系数通常回答一个关键问题：如果某支股票在一个指数中上市且经历价格波动，那么其价格波动中有多少百分比是由该指数的价格波动引起的？

### 关键要点

- 决定系数是一个围绕数据统计分析和金融模型的复杂概念。
- 它用于解释自变量和因变量之间的关系。
- 决定系数通常被称为 r-squared（或 r²），代表其统计值。
- 该测量值介于 0.0 和 1.0 之间，其中 1.0 表示完美的相关性，这是未来预测的可靠模型。
- 0.0 的值则表明资产价格并不依赖于指数的变动。

## 理解决定系数

决定系数是一种测量，用于解释一个因素的变异性有多少是由于其与另一个因素的关系造成的。这种相关性用 0.0 到 1.0 或 0% 到 100% 的值表示。

1.0 的值表明 100% 的价格相关性，是未来预测的可靠模型。0.0 的值则暗示该模型显示价格不依赖于指数。[1]

0.20 的值表明 20% 的资产价格波动可以用指数来解释；0.50 的值则指示 50% 的价格波动可以被解释。

**重要提示：** 决定系数是相关系数的平方，相关系数在统计学中通常用 "r" 表示。值 "r" 可以是负数，但 r² 不能是负数，因为 r-squared 是 "r" 自乘的结果，平方的负数始终是正值。[2]

## 计算决定系数

计算决定系数的方式是创建数据的散点图和趋势线。

如果您要绘制 S&P 500 和苹果（AAPL）股票的收盘价格，从12月21日到1月20日的交易日，您将收集如下表中的价格。[3]4

||S&P 日收盘|APPL 日收盘
|---|---|---|
|1月20|$3,972.61|$137.87
|19|$3,898.85|$135.27
|18|$3,928.86|$135.21
|17|$3,990.97|$135.94
|13|$3,999.09|$134.76
|12|$3,983.17|$133.41
|11|$3,969.61|$133.49
|10|$3,919.25|$130.73
|9|$3,892.09|$130.15
|6|$3,895.08|$129.62
|5|$3,808.10|$125.02
|4|$3,852.97|$126.36
|3|$3,824.14|$125.07
|12月30|$3,839.50|$139.93
|29|$3,849.28|$129.61
|28|$3,783.22|$126.04
|27|$3,829.25|$130.03
|23|$3,844.82|$131.86
|22|$3,822.39|$132.23
|21|$3,878.44|$135.45

接下来，您将创建一个散点图。数据与回归模型的拟合程度被称为拟合优度，它衡量趋势线与图中散布所有数据点之间的距离。

大多数电子表格使用相同的公式来计算数据集的 r²。如果数据位于您的表格的 A 列和 B 列中：

使用该公式并突出显示对应的单元格以获取 S&P 500 和苹果价格的 r² 为 0.347，这表明这两者的相关性较低，而如果 r² 在 0.5 和 1.0 之间，则相关性更高。

手动计算决定系数涉及几个步骤。首先，收集上述表格中的数据，然后计算所需的所有数值，如下表所示：[4]

- x = S&P 500 日收盘
- y = APPL 日收盘

||x|x²|y|y²|xy
|---|---|---|---|---|---|
|1月20|$3,972.61|$15,781,630.21|$137.87|$19,008.14|$547,703.74
|19|$3,898.85|$15,201,031.32|$135.27|$18,297.97|$527,397.44
|18|$3,928.86|$15,435,940.90|$135.21|$18,281.74|$531,221.16
|17|$3,990.97|$15,927,841.54|$135.94|$18,479.68|$542,532.46
|13|$3,999.09|$15,992,720.83|$134.76|$18,160.26|$538,917.37
|12|$3,983.17|$15,865,643.25|$133.41|$17,798.23|$531,394.71
|11|$3,969.61|$15,757,803.55|$133.49|$17,819.58|$529,903.24
|10|$3,919.25|$15,360,520.56|$130.73|$17,090.33|$512,363.55
|9|$3,892.09|$15,148,364.57|$130.15|$16,939.02|$506,555.51
|6|$3,895.08|$15,171,648.21|$129.62|$16,801.34|$504,880.27
|5|$3,808.10|$14,501,625.61|$125.02|$15,630.00|$476,088.66
|4|$3,852.97|$14,845,377.82|$126.36|$15,966.85|$486,861.29
|3|$3,824.14|$14,624,046.74|$125.07|$15,642.50|$478,285.19
|12月30|$3,839.50|$14,741,760.25|$139.93|$19,580.40|$537,261.24
|29|$3,849.28|$14,816,956.52|$129.61|$16,798.75|$498,905.18
|28|$3,783.22|$14,312,753.57|$126.04|$15,886.08|$476,837.05
|27|$3,829.25|$14,663,155.56|$130.03|$16,907.80|$497,917.38
|23|$3,844.82|$14,782,640.83|$131.86|$17,387.06|$506,977.97
|22|$3,822.39|$14,610,665.31|$132.23|$17,484.77|$505,434.63
|21|$3,878.44|$15,042,296.83|$135.45|$18,346.70|$525,334.70
|总和 (Σ)|$77,781.69|$302,584,424.00|$2,638.05|$348,307.23|$10,262,772.73

使用此公式并替换表中每行的值，其中 n 等于样本数量。在本例中是 20：

$$ \begin{aligned}&r ^ 2 = \Big ( \frac {n ( \sum xy) - ( \sum x )( \sum y ) }{ \sqrt { [ n \sum x ^ 2 - ( \sum x ) ^ 2 ] } \times \sqrt { [ n \sum y ^ 2 - ( \sum y ) ^ 2 ] } } \Big ) ^ 2 \\\end{aligned} $$

其中 √ 表示括号内结果的平方根。

$$ \begin{aligned}&r ^ 2 = \Big ( \tiny { \frac {20 ( 10,262,772.73) - ( 77,781.69 )( 2,638.05 ) }{ \sqrt { [ 20 ( 302,584,424 ) - ( 77,781.69 ) ^ 2 ] } \times \sqrt { [ 20 ( 348,307.23 ) - ( 2,638.05 ) ^ 2 ] } } } \Big ) ^ 2 \\\end{aligned} $$

现在得到：

$$ \begin{aligned}&1. \tiny { ( 20 \times 10,262,772.73 ) - ( 77,781.69 \times 2,638.05 ) = 63,467.32 } \\&2. \tiny { (\sqrt { ( 20 \times 302,584,424 ) - ( 77,781.69 ) ^ 2 } = \sqrt { 1,697,180.74 } = 1,302.76 } \\&3. \tiny { (\sqrt { ( 20 \times 10,262,772.73 ) - ( 2,638.05 ) ^ 2 } = \sqrt { 6,836.85 } = 82.69 }\\\end{aligned} $$

现在将步骤二和三相乘，将步骤一除以结果，并平方它：

$$ \begin{aligned}&\Big ( \frac { 63,467.32 }{ 1,302.76 \times 82.69 } \Big ) ^ 2 = 0.347\end{aligned} $$

您可以看到，这在处理几周的交易数据时可能会变得非常繁琐，且容易出错。

## 解释决定系数

当你得到决定系数时，可以用它来评估你所评价的资产价格波动与一个指数或基准的价格波动之间的相关程度。在苹果与 S&P 500 的例子中，该时期的决定系数为 0.347。

**提示：** 苹果公司在许多指数中上市，因此您可以计算 r² 以确定它是否与其他指数的价格波动相对应。

决定系数为 0.357 表明苹果股票价格波动与该指数有一定的相关性，因为 1.0 表示高度相关，而 0.0 则表示没有相关性。

需要注意的一点是，r-squared 并不告诉分析师决定系数的值本质上是好是坏。评估这一相关性及其在未来趋势分析中的应用是分析师的自由裁量。

## 如何解释决定系数？

决定系数显示了一个因变量与一个自变量之间的相关程度。它也被称为 r² 或 r-squared。该值应在 0.0 和 1.0 之间。越接近 0.0，因变量的相关性越小；越接近 1.0，相关性越大。[1]

## R-squared 在回归中告诉你什么？

在回归中，r-squared 告诉你两个值之间是否存在依赖关系，以及一个值对另一个值的依赖程度。

## 如果决定系数大于1会怎样？

决定系数不能大于 1，因为公式始终会返回一个在 0.0 和 1.0 之间的数字。如果超过或低于这些数字，则说明存在错误。[1]

## 结论

决定系数是一个比率，展示了一个变量对另一个变量的依赖程度。投资者利用它来判断资产价格波动与其列出的指数之间的相关性。

当资产的 r² 更接近零时，表明其价格波动并不依赖于指数。如果其 r² 更接近1.0，则表明它更依赖于指数的价格波动。

## 参考文献

[1] Pennsylvania State University Eberly College of Science. "[STAT 462 Applied Regression Analysis: 2.5 - The Coefficient of Determination, r-squared](https://online.stat.psu.edu/stat462/node/95/)."

[2] LibreTexts Statistics. "[10.6: The Coefficient of Determination](https://stats.libretexts.org/Bookshelves/Introductory_Statistics/Introductory_Statistics_%28Shafer_and_Zhang%29/10%3A_Correlation_and_Regression/10.06%3A_The_Coefficient_of_Determination)."

[3] Nasdaq. "[APPL Historical Data](https://www.nasdaq.com/market-activity/stocks/aapl/historical)."

[4] Nasdaq. "[SPX Historical Data](https://www.nasdaq.com/market-activity/index/spx/historical)."

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
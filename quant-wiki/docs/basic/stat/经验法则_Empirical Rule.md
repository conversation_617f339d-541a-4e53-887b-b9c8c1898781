![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 经验法则是什么？
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)
经验法则，也被称为三西格玛法则或68-95-99.7法则，是一条统计规则，表明在正态分布中，几乎所有观察到的数据都会落在均值（用希腊字母μ表示）±三倍标准差（用希腊字母σ表示）之内。

具体来说，经验法则预测，在正态分布中，68%的观察值将落在第一个标准差范围内（μ ± σ），95%将在前两个标准差范围内（μ ± 2σ），99.7%将在前三个标准差范围内（μ ± 3σ）。

### 关键要点
- 经验法则指出，在正态分布中，几乎所有观察数据将落在均值的±三倍标准差内。
- 根据该规则，68%的数据将在一个标准差内，95%的数据将在两个标准差内，99.7%的数据将在三个标准差内。
- 遵循经验法则的三西格玛限用于在统计质量控制图和风险分析中设定上下控制限。

## 理解经验法则

经验法则常用于统计学中进行最终结果的预测。在计算出标准差后，在收集完整数据之前，可以将该规则作为即将收集和分析的数据结果的粗略估算。

这种概率分布可以作为评估技术，因为在某些情况下，收集适当的数据可能会耗时或甚至不可能。在公司审查其质量控制措施或评估风险暴露时，这样的考虑尤为重要。例如，常用的风险工具“风险价值（VaR）”假设风险事件的概率分布为正态分布。

经验法则还用作测试分布“正态性”的粗略方法。如果太多数据点落在三个标准差的边界之外，则表明该分布不是正态的，可能存在偏斜或遵循其他分布。

经验法则又被称为三西格玛法则，因为“三西格玛”指的是数据在正态分布（钟形曲线）中均值±三倍标准差的统计分布，如下图所示。

## 经验法则示例

假设一个动物园中的动物寿命呈正态分布。每只动物的平均寿命为13.1岁（均值），寿命的标准差为1.5岁。如果有人想知道一只动物活过14.6岁的概率，他们可以使用经验法则。已知该分布的均值为13.1岁，以下是每个标准差所对应的年龄范围：

- 一个标准差（μ ± σ）：（13.1 - 1.5）到（13.1 + 1.5），即11.6到14.6
- 两个标准差（μ ± 2σ）：13.1 - (2 × 1.5) 到 13.1 + (2 × 1.5)，即10.1到16.1
- 三个标准差（μ ± 3σ）：13.1 - (3 × 1.5) 到 13.1 + (3 × 1.5)，即8.6到17.6

解决此问题的人需要计算动物活到14.6岁或更长时间的总概率。经验法则显示，68%的分布位于一个标准差范围内，即11.6到14.6岁。因此，分布中剩余的32%位于这个范围之外。一半在14.6岁以上，另一半在11.6岁以下。因此，动物活过14.6岁的概率为16%（计算方法为32%除以2）。

## 经验法则在投资中的应用

大多数市场数据并不是正态分布，因此68-95-99.7法则一般不适用于投资。然而，许多分析师仍然使用其中的一些方面——例如标准差——来估计波动率。

您可以计算投资组合、指数或其他投资的标准差，并用其来评估波动率。如果您能获取电子表格和所选投资的价格或收益，计算某项投资的标准差是简单的。

市场分析师一般以百分比形式表达标准差。例如，2023年5月2日至6月2日，标准普尔500指数（使用每日收盘价）的日标准差（年化）为13.29%。

在电子表格中，您可以将收益、价格或数值粘贴进去，计算与前一个交易日的百分比变化，并使用标准差函数：1

**重要提示：**使用超过一个月的交易数据，例如三年或更多，会得到更准确的结果。下面的示例使用该指数一个月内的每日值，并将标准差年化以限制表格大小。

要年化标准差，将其乘以一年中的交易日数的平方根——通常为252。

| 标准普尔500标准差（年化）
|---|---|---|---|
||A|B|C|D
|1|日期|收盘|日间变动|公式
|2|05/02/23|4119.58|-|-
|3|05/03/23|4090.75|-0.70%|-
|4|05/04/23|4061.22|-0.72%|-
|5|05/05/23|4136.25|1.85%|-
|6|05/08/23|4138.12|0.05%|-
|7|05/09/23|4119.17|-0.46%|-
|8|05/10/23|4137.64|0.45%|-
|9|05/11/23|4130.62|-0.17%|-
|10|05/12/23|4124.08|-0.16%|-
|11|05/15/23|4136.28|0.30%|-
|12|05/16/23|4109.9|-0.64%|-
|13|05/17/23|4158.77|1.19%|-
|14|05/18/23|4198.05|0.94%|-
|15|05/19/23|4191.98|-0.14%|-
|16|05/22/23|4192.63|0.02%|-
|17|05/23/23|4145.58|-1.12%|-
|18|05/24/23|4115.24|-0.73%|-
|19|05/25/23|4151.28|0.88%|-
|20|05/26/23|4205.45|1.30%|-
|21|05/30/23|4205.52|0.00%|-
|22|05/31/23|4179.83|-0.61%|-
|23|06/01/23|4221.02|0.99%|-
|24|06/02/23|4282.37|1.45%|-
|25|-|日标准差|0.84%|=stdev(B2:B24)
|26|-|年化标准差|13.29%|=sqrt(252)*C25

因此，基于表中数据的年化波动率为13.29%。标准差越高，分析师认为投资的风险就越大。

另外，您也可以在流行的投资网站上找到某项投资的标准差。例如，晨星会显示标准普尔500的三年、五年和十年的标准差。 [2]

## 经验法则是什么？

在统计学中，经验法则指出，在正态分布中，99.7%的观察数据将落在均值的±三倍标准差内。具体来说，68%的观察数据将在一个标准差内，95%将在两个标准差内，97.5%将在三个标准差内。

## 经验法则如何使用？

经验法则应用于预测正态分布中的可能结果。例如，统计学家可以用它来估算每个标准差范围内的案例百分比。假设标准差为3.1，均值为10。在这种情况下，第一个标准差的范围将在（10+3.2)=13.2和（10-3.2)=6.8之间。第二个标准差将在10 + (2 × 3.2)=16.4和10 - (2 × 3.2)=3.6之间，依此类推。

## 经验法则的好处是什么？

经验法则的好处在于它可以作为预测数据的一种手段。这在处理大型数据集和变量未知的数据时尤为重要。

## 总结

分析师使用经验法则来观察数据集中有多少数据落在距离均值的特定区间内。投资分析师可以利用它来估算特定投资、投资组合或基金的波动率。

## 参考文献

[1] Google Docs Editors Help. "[STDEV](https://support.google.com/docs/answer/3094054?hl=en)."

[2] Morningstar. "[S&P 500 PR](https://www.morningstar.com/indexes/spi/spx/risk)."

## 关于LLMQuant
LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。
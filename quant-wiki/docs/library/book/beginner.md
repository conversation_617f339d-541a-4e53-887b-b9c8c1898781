# 量化金融入门书籍推荐

以下是为量化金融初学者推荐的入门级书籍：

## 金融数学基础

- [A Linear Algebra Primer for Financial Engineering](金融数学/A%20Linear%20Algebra%20Primer%20for%20Financial%20Engineering/index.md)
  - 难度: ⭐⭐⭐⭐
  - 推荐指数: ⭐⭐⭐⭐⭐
  - 内容: 金融工程中的线性代数应用

## 量化交易入门

- [101 Formulaic Alphas](量化交易/101%20Formulaic%20Alphas%20-%20arXiv.org/index.md)
  - 难度: ⭐⭐⭐⭐
  - 推荐指数: ⭐⭐⭐⭐⭐
  - 内容: 因子投资入门经典

## 量化面试准备

- [Quant绿皮书精讲60题](量化面试/Quant绿皮书精讲60题/index.md)
  - 难度: ⭐⭐⭐⭐
  - 推荐指数: ⭐⭐⭐⭐⭐
  - 内容: 量化面试题目精讲

## 学习路径建议

1. 首先掌握必要的数学工具
2. 学习基础的因子策略
3. 了解量化交易基本概念
4. 准备量化面试题目

# 量化交易入门书籍推荐

## 《Python量化交易入门》

<img src="../images/python-quant-intro.jpg" alt="Python量化交易入门" width="200"/>

- **作者**: 王小川
- **出版社**: 电子工业出版社
- **难度**: ⭐⭐
- **推荐指数**: ⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/python-quant-intro.pdf)

### 内容简介

本书从Python基础开始，逐步介绍量化交易的核心概念和实践方法。适合零基础的读者入门量化交易。

### 核心章节

1. Python基础知识
2. 数据获取与处理
3. 技术分析指标
4. 策略回测系统
5. 实盘交易接口

### 配套资源

- 源代码: [GitHub链接]
- 数据集: [示例数据]
- 习题解答: [在线文档]

---

## 《量化投资策略与技术》

<img src="../images/quant-strategy.jpg" alt="量化投资策略与技术" width="200"/>

- **作者**: 丁鹏
- **出版社**: 电子工业出版社
- **出版年份**: 2012
- **难度**: ⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐

### 内容简介

本书系统地介绍了量化投资的基本概念、策略开发方法和风险管理技术。全书通过60多个案例，深入探讨了量化选股、量化择时、股指期货套利、商品期货套利、统计套利、期权套利、算法交易和资产配置等多种量化投资策略。在数学技术方面，本书涵盖了人工智能、数据挖掘、小波分析、支持向量机、分形理论、随机过程等先进的数学工具，并详细阐述了这些技术在金融领域的具体应用。本书适合基金经理、产品经理、证券分析师、投资总监及有志于从事金融投资的各界人士阅读，特别适合具有一定金融基础并希望深入了解量化投资策略与技术的读者。

### 核心章节

1. 量化投资概述
2. 股票量化策略
3. 期货量化策略
4. 统计套利
5. 风险管理

### 配套资源

- 课件下载: [PPT链接]
- 示例代码: [实例程序]
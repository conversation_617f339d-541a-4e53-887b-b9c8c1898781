# Machine Learning in Asset Pricing

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。


- **作者**: <PERSON>gel
- **出版社**: Princeton University Press
- **出版年份**: 2021
- **难度**: ⭐⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Machine Learning in Asset Pricing-Princeton Univ Pr (2021).pdf)

### 内容简介

本书系统地介绍了机器学习在资产定价中的应用，涵盖了从传统方法到最新的深度学习技术，为资产定价研究提供了全新的方法论框架。

### 核心章节

1. 机器学习基础
2. 特征工程与选择
3. 非线性资产定价模型
4. 深度学习应用
5. 风险溢价预测

### 主要特点

- 理论深度强
- 算法实现详细
- 实证研究丰富
- 前沿技术覆盖

### 适合人群

- 量化研究员
- 资产定价研究者
- 机器学习工程师
- 金融博士生

### 配套资源

- Python代码实现
- 研究数据集
- 模型示例
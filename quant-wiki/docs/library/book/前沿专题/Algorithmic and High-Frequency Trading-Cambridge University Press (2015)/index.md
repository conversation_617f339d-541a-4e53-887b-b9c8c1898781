# Algorithmic and High-Frequency Trading

- **作者**: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>
- **出版社**: Cambridge University Press
- **出版年份**: 2015
- **格式**: PDF
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Algorithmic and High-Frequency Trading-Cambridge University Press (2015).pdf)

### 内容简介

本书详细介绍了Algorithmic and High-Frequency Trading相关的核心概念和实践方法。它结合了复杂的数学建模、实证事实和金融经济学，旨在帮助读者理解现代电子市场的运作方式以及如何设计交易算法。书中涵盖了市场微观结构、金融市场基本原理、价格与回报的实证统计证据、活动与市场质量等内容。

在数学技术方面，本书深入探讨了随机最优控制和停止理论。在金融领域的应用方面，本书详细介绍了多种算法交易策略，包括连续交易中的最优执行、限价单和市价单的最优执行、目标成交量加权平均价格（VWAP）及其他交易计划、做市、配对交易和统计套利策略，以及订单不平衡等。此外，书中还包含金融随机微积分附录，为读者提供了坚实的数学基础。本书适合数学金融领域的硕士和博士生，以及希望深入了解现代电子市场和算法交易实践的专业人士。

### 主要特点

- 理论基础扎实
- 实践案例丰富
- 操作指导清晰
- 适合实际应用

### 适合人群

- 量化分析师
- 算法交易员
- 金融工程师
- 数据科学家
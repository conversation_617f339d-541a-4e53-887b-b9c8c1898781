# Empirical Asset Pricing via Machine Learning

- **作者**: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>u
- **出版社**: The Review of Financial Studies (Oxford University Press)
- **出版年份**: 2020
- **格式**: PDF
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Empirical Asset Pricing via Machine Learning.pdf)

### 内容简介

本书深入探讨了机器学习方法在实证资产定价领域的应用，旨在衡量资产风险溢价。作者对多种机器学习技术进行了比较分析，包括广义线性模型、降维方法（如主成分分析PCA、偏最小二乘PLS）、正则化方法（如LASSO、Elastic Net）、集成学习方法（如梯度提升回归树GBRT、随机森林）以及神经网络（包括前馈神经网络和自编码器）等。

本书展示了利用机器学习预测模型为投资者带来的显著经济收益，在某些情况下，其表现甚至能使现有文献中领先的基于回归的策略的收益翻倍。 作者识别出表现最佳的方法（如树模型和神经网络），并将其预测能力的提升归因于它们能够捕捉传统方法所遗漏的非线性预测因子交互作用。 此外，研究发现所有方法都认同少数几个主导性的预测信号，包括动量、流动性和波动性等方面的变体。 本书为衡量总市场和个股风险溢价的机器学习方法提供了新的预测准确性基准，并强调了机器学习在金融创新中日益增长的作用。

### 主要特点

- 理论基础扎实
- 实践案例丰富
- 操作指导清晰
- 适合实际应用

### 适合人群

- 量化分析师
- 算法交易员
- 金融工程师
- 数据科学家
# Algorithmic and High-Frequency Trading

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。


- **作者**: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>
- **出版社**: Cambridge University Press
- **出版年份**: 2015
- **难度**: ⭐⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Algorithmic and High-Frequency Trading-Cambridge University Press (2015).pdf)

### 内容简介

本书深入探讨了算法交易和高频交易的数学模型和实现方法，包括市场微观结构、最优执行、做市策略等核心内容。

### 核心章节

1. 市场微观结构理论
2. 最优执行策略
3. 做市商策略设计
4. 价格发现机制
5. 风险管理方法

### 主要特点

- 理论深度强
- 数学模型严谨
- 实践指导详细
- 案例分析充实

### 适合人群

- 高频交易研究员
- 算法交易开发者
- 量化研究员
- 市场微观结构研究者

### 配套资源

- 数学推导补充
- 代码实现示例
- 数据分析工具
# Machine Learning in Asset Pricing-Princeton Univ Pr (2021)

- **作者**: <PERSON> Nagel
- **出版社**: Princeton University Press
- **出版年份**: 2021
- **格式**: PDF
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Machine Learning in Asset Pricing-Princeton Univ Pr (2021).pdf)

### 内容简介

本书《Machine Learning in Asset Pricing》由Stefan Nagel撰写，是普林斯顿金融讲座系列的一部分，权威性地介绍了机器学习如何应用于资产定价领域。在金融市场中，投资者面临着来自各种来源的大量潜在价值相关信息。在这些数据丰富、高维度的环境中，快速发展的机器学习（ML）技术非常适合解决预测问题。因此，机器学习方法正迅速成为资产定价研究和量化投资工具包的一部分。

本书深入探讨了机器学习在资产定价应用中的前景和挑战。资产定价问题与机器学习工具最初开发的场景有显著不同，因此，为了充分发挥机器学习方法的潜力，必须根据资产定价应用的具体条件进行调整。经济学考量，例如投资组合优化、无套利原则以及投资者学习，可以指导机器学习工具的选择和修改。

本书首先简要概述了基本的监督学习方法，然后讨论了这些技术在资产定价实证研究中的应用，并展示了它们如何有望推动金融市场理论建模的进步。书中涵盖了预测资产之间回报差异、捕捉回报随时间的可预测变化、信用风险预测以及资产回报协动性预测等核心问题。 本书为金融资产估值研究中运用前沿方法提供了令人兴奋的可能性。

### 主要特点

- 理论基础扎实
- 实践案例丰富
- 操作指导清晰
- 适合实际应用

### 适合人群

- 量化分析师
- 算法交易员
- 金融工程师
- 数据科学家
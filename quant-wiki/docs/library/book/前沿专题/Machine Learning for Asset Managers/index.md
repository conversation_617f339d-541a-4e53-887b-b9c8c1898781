# Machine Learning for Asset Managers

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。


- **作者**: <PERSON> P<PERSON>
- **出版社**: Cambridge University Press
- **出版年份**: 2020
- **难度**: ⭐⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Machine Learning for Asset Managers-Cambridge University Press (2020).pdf)

### 内容简介

本书由量化金融专家Marcos López de Prado撰写，系统介绍了机器学习在资产管理领域的创新应用。它旨在帮助资产管理者利用机器学习工具发现经济和金融理论，克服传统统计方法在处理复杂金融系统时的局限性。书中涵盖了机器学习基础、投资组合优化、因子选择方法、风险管理技术以及策略回测框架等核心章节。

本书深入探讨了多种数学技术及其在金融领域的应用，包括但不限于数据去噪、距离度量、最优聚类、金融标签、特征重要性分析以及投资组合构建。 它强调了机器学习在提升样本外预测能力、处理非线性关系和高维数据方面的优势，并提供了实用的计算方法和Python代码示例，旨在帮助读者将理论应用于实践，从而在资产管理中做出更明智的决策。

### 核心章节

1. 机器学习基础
2. 投资组合优化
3. 因子选择方法
4. 风险管理技术
5. 策略回测框架

### 主要特点

- 理论创新性强
- 实践指导详实
- 技术前沿性高
- 方法论系统

### 适合人群

- 量化基金经理
- 机器学习研究员
- 量化策略开发者
- 风险管理师

### 配套资源

- Python代码实现
- 研究数据集
- 策略回测工具
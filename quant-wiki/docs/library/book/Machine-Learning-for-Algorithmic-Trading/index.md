# Machine-Learning-for-Algorithmic-Trading

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。


- **作者**: <PERSON> Jansen
- **出版社**: Packt Publishing
- **出版年份**: 2020
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Machine%20Learning%20for%20Algorithmic%20Trading.pdf)

### 内容简介

Machine Learning for Algorithmic Trading 是一本关于量化金融的专业书籍，深入探讨了机器学习在算法交易中的端到端应用。本书详细介绍了如何利用机器学习技术来优化交易策略，包括从市场、基本面和另类数据（如高频数据、SEC文件、财报电话会议记录、金融新闻甚至卫星图像）中进行数据预处理和特征工程，特别是如何研究和评估金融特征或Alpha因子。书中涵盖了广泛的机器学习技术，从线性模型、基于树的集成方法到深度学习（如CNN、RNN、自编码器）、自然语言处理（NLP）以及生成对抗网络（GANs）等，并详细阐述了如何设计、训练、优化模型以及进行策略回测。本书不仅包含丰富的理论知识，还提供了大量实际案例和使用Python库（如pandas, scikit-learn, TensorFlow, Zipline, backtrader, Alphalens, pyfolio等）的代码示例，旨在帮助读者将机器学习模型预测转化为实际的交易策略，并有效评估其表现和进行投资组合优化。


### 主要特点

- 理论与实践结合
- 包含详细示例
- 配套代码和资源
- 适合实际应用

### 适合人群

- 量化分析师
- 算法交易员
- 金融工程师
- 数据科学家

### 配套资源

- 示例代码
- 数据集
- 在线补充材料
# Artificial Intelligence in Finance- A Python-Based Guide

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。


- **作者**: Yves J. Hilpisch
- **出版社**: O'Reilly Media
- **出版年份**: 2020
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Artificial Intelligence in Finance_ A Python-Based Guide.pdf)

### 内容简介

《Artificial Intelligence in Finance: A Python-Based Guide》是一本实用的金融专业书籍，深入探讨了如何利用人工智能和机器学习技术来发现金融市场中的统计低效性，并通过算法交易进行利用。本书详细介绍了机器学习和深度学习算法在金融领域的应用，包括神经网络和强化学习等核心数学技术。书中通过大量的Python示例，帮助读者理解数据如何重塑金融行业，并掌握将AI应用于金融的工具、技能和主要用例。内容涵盖了从AI的基本概念到如何通过回测和自动化交易策略来识别和利用经济低效性，旨在提升读者在量化金融领域的专业能力，无论是金融从业者、学生还是学术研究者，都能从中获得宝贵的知识和实践经验。


### 主要特点

- 理论与实践结合
- 包含详细示例
- 配套代码和资源
- 适合实际应用

### 适合人群

- 量化分析师
- 算法交易员
- 金融工程师
- 数据科学家

### 配套资源

- 示例代码
- 数据集
- 在线补充材料
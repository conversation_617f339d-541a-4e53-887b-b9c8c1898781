# Quantum Machine Learning and Optimisation in Finance- On the Road to Quantum Advantage

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。

<img src="1.png" alt="Quantum Machine Learning and Optimisation in Finance- On the Road to Quantum Advantage" width="200"/>

- **作者**: <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>
- **出版社**: Packt Publishing
- **出版年份**: 2022
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Quantum Machine Learning and Optimisation in Finance_ On the Road to Quantum Advantage.pdf)

### 内容简介

Quantum Machine Learning and Optimisation in Finance- On the Road to Quantum Advantage 是一本关于量化金融的专业书籍，涵盖了量子机器学习和优化在金融领域的应用。本书深入探讨了如何利用噪声中等规模量子（NISQ）硬件创建混合量子-经典机器学习和优化模型，以期在金融应用中实现量子优势。书中详细介绍了可应用于现有NISQ设备的各种主要量子计算算法，并强调了量子计算范式在金融领域的实际应用潜力。

本书内容分为两大部分：第一部分聚焦于模拟量子计算，特别是量子退火，涵盖了绝热量子计算、二次无约束二元优化（QUBO）、量子提升和量子玻尔兹曼机等主题。第二部分则侧重于门模型量子计算，包括量子比特与量子逻辑门、参数化量子电路与数据编码、量子神经网络、量子电路玻尔兹曼机、变分量子本征求解器（VQE）以及量子近似优化算法（QAOA）等。本书旨在帮助读者掌握量子机器学习的原理，并将其应用于解决金融中的复杂优化问题，例如训练参数化量子电路作为生成模型，以及利用量子提升技术。

### 核心章节

以下是本书的主要章节预览：

![Chapter 2](2.png)

![Chapter 3](3.png)

![Chapter 4](4.png)

![Chapter 5](5.png)

![Chapter 6](6.png)

![Chapter 7](7.png)

![Chapter 8](8.png)

![Chapter 9](9.png)

### 主要特点

- 理论与实践结合
- 包含详细示例
- 配套代码和资源
- 适合实际应用

### 适合人群

- 量化分析师
- 算法交易员
- 金融工程师
- 数据科学家

### 配套资源

- 示例代码
- 数据集
- 在线补充材料
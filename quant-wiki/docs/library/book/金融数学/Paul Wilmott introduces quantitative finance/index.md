# <PERSON> Wilmott Introduces Quantitative Finance

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。

- **作者**: <PERSON> W<PERSON>mott
- **出版社**: Wiley
- **出版年份**: 2007
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/<PERSON> Wilmott - <PERSON> Wilmott introduces quantitative finance-Wiley (2007).pdf)

### 内容简介

本书是量化金融的入门经典，由著名金融数学家Paul Wilmott编写，以清晰的方式介绍了量化金融的核心概念和方法。本书深入浅出地涵盖了金融数学基础，包括随机过程、随机微积分和偏微分方程等核心数学工具。它详细阐述了各类衍生品的定价理论与实践，如期权、期货等，并探讨了Black-Scholes模型及其“希腊字母”的应用。此外，书中还系统介绍了风险管理的关键技术，包括对冲策略、VaR计算和信用风险分析。在利率模型方面，本书涵盖了固定收益产品、利率衍生品以及主流的利率建模方法，如HJM和BGM模型。最后，本书还重点讲解了在量化金融中广泛应用的数值方法，如有限差分法、蒙特卡洛模拟和数值积分等，帮助读者理解如何将理论模型应用于实际问题解决。本书旨在为读者提供一个全面而实用的量化金融知识体系，强调数学技术在金融领域的实际应用。

### 核心章节

1. 金融数学基础
2. 衍生品定价
3. 风险管理
4. 利率模型
5. 数值方法

### 主要特点

- 讲解通俗
- 案例丰富
- 实用性强
- 覆盖面广

### 适合人群

- 金融工程学生
- 量化分析师
- 风险管理师
- 投资经理

### 配套资源

- Excel模型
- 代码示例
- 在线补充材料
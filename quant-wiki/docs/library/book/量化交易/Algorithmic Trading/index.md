# Algorithmic Trading: Winning Strategies and Their Rationale

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。

<img src="cover.jpg" alt="Algorithmic Trading" width="200"/>

- **作者**: <PERSON>. <PERSON>
- **出版社**: Wiley
- **出版年份**: 2013
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Algorithmic Trading_ Winning Strategies and Their Rationale-Wiley (2013).pdf)

### 内容简介

本书由量化交易专家Ernest <PERSON>. Chan撰写，深入探讨了算法交易策略的开发、测试与实现过程。书中不仅详细介绍了统计套利、动量交易和均值回归等多种核心策略，更强调了将科学方法应用于策略开发的实践性。作者结合自身实战经验，阐述了如何利用统计模型、高级计算机算法以及如卡尔曼滤波、协整性检验等数学技术，从海量金融数据中发现模式和趋势。本书提供了完整的策略开发框架，并涵盖了在股票、ETF、货币和期货等多种金融工具中的应用，同时深入讨论了风险管理和策略评估方法，旨在帮助读者构建和优化自己的自动化交易系统。

### 核心章节

1. 策略开发框架
2. 统计套利策略
3. 动量与均值回归
4. 机器学习应用
5. 策略评估方法

### 主要特点

- 实用性强
- 代码示例丰富
- 策略完整
- 实践指导详细

### 适合人群

- 量化策略研究员
- 算法交易开发者
- 自动化交易者
- 量化分析师

### 配套资源

- MATLAB代码
- 策略回测框架
- 数据处理工具
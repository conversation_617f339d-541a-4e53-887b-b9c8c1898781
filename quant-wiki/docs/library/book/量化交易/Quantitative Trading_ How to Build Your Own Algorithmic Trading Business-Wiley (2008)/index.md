# Quantitative Trading- How to Build Your Own Algorithmic Trading Business

- **作者**: <PERSON>
- **出版社**: Wiley
- **出版年份**: 2008
- **格式**: PDF
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/Quantitative%20Trading_%20How%20to%20Build%20Your%20Own%20Algorithmic%20Trading%20Business-Wiley%20(2008).pdf)

### 内容简介

本书《Quantitative Trading: How to Build Your Own Algorithmic Trading Business》由Ernest P. Chan撰写，旨在指导独立交易者和有志于在金融机构从事量化交易的个人，如何建立自己的算法交易业务。书中详细介绍了量化交易的核心概念和实践方法，强调了将理论知识应用于实际操作的重要性。

本书涵盖了构建算法交易系统所需的关键数学技术和在金融领域的应用。内容包括但不限于：如何识别和开发适合自己的交易策略，这涉及到对交易时间、编程技能和交易资本的考量。书中深入探讨了回测（Backtesting）的重要性，并介绍了常见的平台如Excel、MATLAB、Python和R，同时指出了回测中常见的陷阱，如未来数据偏差（Look-Ahead Bias）和数据窥探偏差（Data-Snooping Bias）。

此外，本书还详细讲解了资金管理和风险管理，包括最优资本配置和杠杆使用，以及风险控制策略。书中还涉及了执行系统（Execution Systems）的构建，包括半自动化和全自动化交易系统，以及如何最小化交易成本。特别主题部分探讨了均值回归与动量策略、市场机制转换、平稳性与协整性、因子模型以及退出策略等。本书旨在提供一个全面的指南，帮助读者将技术专长与商业敏锐度相结合，成功构建和运营盈利的算法交易业务。

### 主要特点

- 理论基础扎实
- 实践案例丰富
- 操作指导清晰
- 适合实际应用

### 适合人群

- 量化分析师
- 算法交易员
- 金融工程师
- 数据科学家
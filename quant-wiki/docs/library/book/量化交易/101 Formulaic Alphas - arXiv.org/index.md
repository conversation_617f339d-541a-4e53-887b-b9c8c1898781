# 101 Formulaic Alphas

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。

<img src="cover.jpg" alt="101 Formulaic Alphas" width="200"/>

- **作者**: <PERSON><PERSON>
- **出版社**: arXiv.org
- **出版年份**: 2016
- **难度**: ⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/101 Formulaic Alphas - arXiv.org.pdf)

### 内容简介

本文由Zura Kakushadze撰写，详细介绍了101个真实世界量化交易因子的显式公式和实现方法，这些公式同时也是可执行的计算机代码。该论文是量化投资领域的经典之作，旨在揭示现代量化交易的奥秘，为量化研究提供了丰富的思路和实践指导。书中涵盖的因子主要基于价格和成交量数据（如日收盘价、开盘价、最高价、最低价、成交量和VWAP），部分因子也融入了基本面数据（如市值）和行业分类（如GICS、BICS等）进行行业中性化处理。这些因子主要应用于短期量化交易策略，平均持有期约为0.6-6.4天。通过提供这些实用的因子，本书为量化研究员、因子投资者和策略开发者提供了宝贵的资源，帮助他们理解和构建有效的量化交易策略。

### 核心章节

1. 价格动量因子
2. 成交量因子
3. 波动率因子
4. 相关性因子
5. 技术指标因子

### 主要特点

- 公式明确
- 实现简单
- 覆盖面广
- 实用性强

### 适合人群

- 量化研究员
- 因子投资者
- 策略开发者
- 学术研究者

### 配套资源

- Python实现代码
- 因子测试框架
- 研究数据集
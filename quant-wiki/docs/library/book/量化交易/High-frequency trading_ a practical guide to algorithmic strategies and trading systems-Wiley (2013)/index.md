# High-frequency Trading

![](https://fastly.jsdelivr.net/gh/bucketio/img3@main/2024/09/04/1725464231869-e0b2f727-2a0f-4270-bf6c-31ddc350426a.gif)
本书籍由[LLMQuant社区](https://llmquant.com/)整理, 并提供PDF下载, 只供学习交流使用, 版权归原作者所有。

<img src="cover.jpg" alt="High-frequency Trading" width="200"/>

- **作者**: <PERSON> Aldridge
- **出版社**: Wiley
- **出版年份**: 2013
- **难度**: ⭐⭐⭐⭐⭐
- **推荐指数**: ⭐⭐⭐⭐⭐
- **PDF下载**: [点击下载](https://github.com/LLMQuant/asset/blob/main/High-frequency trading_ a practical guide to algorithmic strategies and trading systems-Wiley (2013).pdf)

### 内容简介

本书全面介绍了高频交易（HFT）的理论基础、策略开发和系统实现，包括市场微观结构、高频数据处理、策略设计等核心内容。书中深入探讨了多种量化交易策略，如市场微观结构套利、事件套利和统计套利，并详细阐述了它们在股票、期货等金融工具中的实际应用。此外，本书还介绍了高频交易中使用的关键数学技术，包括线性与非线性计量经济模型、波动率建模、傅里叶分析以及用于分析高频数据和评估交易表现（如收益、波动率、回撤、Alpha、Beta、偏度、峰度）的统计方法。同时，本书也涵盖了高频交易特有的风险管理、投资组合管理技术以及系统架构设计，为读者提供了理解和实施高频交易的完整框架。

### 核心章节

1. 市场微观结构
2. 高频数据处理
3. 策略开发方法
4. 风险管理
5. 系统架构设计

### 主要特点

- 理论深入
- 实践指导详细
- 技术要求高
- 系统性强

### 适合人群

- 高频交易开发者
- 量化研究员
- 系统架构师
- 市场微观结构研究者

### 配套资源

- 策略示例代码
- 数据处理工具
- 系统架构参考
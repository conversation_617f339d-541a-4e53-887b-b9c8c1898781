## 再现研究成果培训与书籍

![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
本列表由[LLMQuant社区](https://llmquant.com/)整理, 只供学习交流使用, 版权归原作者所有。

- [Auto-Differentiation Website](https://auto-differentiation.github.io/) - 有关自动微分（AD）/伴随算法微分（AAD）的背景知识与资源。
- [Derman Papers](https://github.com/MarcosCarreira/DermanPapers) - Emanuel Derman 量化金融论文的重现与笔记。
- [ML-Quant](https://www.ml-quant.com/) - 量化方向的优秀资源索引，如 ArXiv（sanity）、SSRN、RePec、期刊、播客、视频和博客等。
- [volatility-trading](https://github.com/jasonstrimpel/volatility-trading) - 依据 Euan Sinclair 的《Volatility Trading》收集的各种波动率估计器。
- [quant](https://github.com/paulperry/quant) - 定量金融与算法交易资源汇总；主要是基于 Quantopian、Zipline 或 Pandas 的 notebook。
- [fecon235](https://github.com/rsvp/fecon235) - 针对金融经济学的开源项目，包含许多交互式的 Jupyter notebook，用于验证理论和实践方法。
- [Quantitative-Notebooks](https://github.com/LongOnly/Quantitative-Notebooks) - 关于量化金融、算法交易、金融建模和投资策略的教学 notebook。
- [QuantEcon](https://quantecon.org/) - 经济学、金融、计量经济学和数据科学的系列讲座；提供 QuantEcon.py、QuantEcon.jl 和相关 notebooks。
- [FinanceHub](https://github.com/Finance-Hub/FinanceHub) - 定量金融资源整合。
- [Python_Option_Pricing](https://github.com/dedwards25/Python_Option_Pricing) - 完整的期权定价 Python 库，包括 Black-Scholes、Black 76、隐含波动率、美式/欧式/亚式/价差期权等。
- [python-training](https://github.com/jpmorganchase/python-training) - J.P. Morgan 提供的面向业务分析师和交易员的 Python 培训。
- [Stock_Analysis_For_Quant](https://github.com/LastAncientOne/Stock_Analysis_For_Quant) - 使用 Excel、Matlab、Power BI、Python、R、Tableau 等对股票进行不同类型的分析。
- [algorithmic-trading-with-python](https://github.com/chrisconlan/algorithmic-trading-with-python) - 《Algorithmic Trading with Python (2020)》的源码与资源。
- [MEDIUM_NoteBook](https://github.com/cerlymarco/MEDIUM_NoteBook) - 包含 [cerlymarco](https://github.com/cerlymarco) 在 Medium 上文章的源码 notebook。
- [QuantFinance](https://github.com/PythonCharmers/QuantFinance) - 用于定量金融的培训材料。
- [IPythonScripts](https://github.com/mgroncki/IPythonScripts) - 包含量化金融与 QuantLib 在 Python 中的各种示例与教程，包括定价、xVAs、对冲、投资组合优化、机器学习与深度学习。
- [Computational-Finance-Course](https://github.com/LechGrzelak/Computational-Finance-Course) - 计算金融课程的教学材料。
- [Machine-Learning-for-Asset-Managers](https://github.com/emoen/Machine-Learning-for-Asset-Managers) - 《Machine Learning for Asset Managers》一书中代码段的实现、练习及对实时数据的应用。
- [Python-for-Finance-Cookbook](https://github.com/PacktPublishing/Python-for-Finance-Cookbook) - Packt 出版的《Python for Finance Cookbook》源码。
- [modelos_vol_derivativos](https://github.com/ysaporito/modelos_vol_derivativos) - 书籍《Modelos de Volatilidade para Derivativos》中的 Jupyter notebooks。
- [NMOF](https://github.com/enricoschumann/NMOF) - 《Numerical Methods and Optimization in Finance》第一、二版相关函数、示例与数据。
- [py4fi2nd](https://github.com/yhilpisch/py4fi2nd) - 《Python for Finance (2nd ed., O'Reilly)》一书的 Jupyter notebook 与源码。
- [aiif](https://github.com/yhilpisch/aiif) - Yves Hilpisch 著《Artificial Intelligence in Finance (O'Reilly)》的 Jupyter notebook 与源码。
- [py4at](https://github.com/yhilpisch/py4at) - Yves Hilpisch 著《Python for Algorithmic Trading (O'Reilly)》的相关代码与 notebook。
- [dawp](https://github.com/yhilpisch/dawp) - 《Derivatives Analytics with Python (Wiley Finance)》一书的 notebook 与代码。
- [dx](https://github.com/yhilpisch/dx) - DX Analytics | 利用 Python 进行金融及衍生品分析。
- [QuantFinanceBook](https://github.com/LechGrzelak/QuantFinanceBook) - 《Quantitative Finance》书籍的相关资源。
- [rough_bergomi](https://github.com/ryanmccrickerd/rough_bergomi) - Rough Bergomi 模型的 Python 实现。
- [frh-fx](https://github.com/ryanmccrickerd/frh-fx) - Mechkov 提出的 fast-reversion Heston 模型的 Python 实现，主要针对外汇场景。
- [Value Investing Studies](https://github.com/euclidjda/value-investing-studies) - 收集基于价值投资理念的长期或短期历史数据分析研究。
- [Machine Learning Asset Management](https://github.com/firmai/machine-learning-asset-management) - 资产管理中的机器学习相关示例与资源（by @firmai）。
- [Deep Learning Machine Learning Stock](https://github.com/LastAncientOne/Deep-Learning-Machine-Learning-Stock) - 将深度学习和机器学习应用于股票市场，展示其潜在的长短期投资机会。
- [Technical Analysis and Feature Engineering](https://github.com/jo-cho/Technical_Analysis_and_Feature_Engineering) - 在金融市场中进行特征工程和特征重要性评估的示例。
- [Differential Machine Learning and Axes that matter by Brian Huge and Antoine Savine](https://github.com/differential-machine-learning/notebooks) - 实现并扩展 Risk 文章《Differential Machine Learning》（2020）与《PCA with a Difference》（2021）的结果。
- [systematictradingexamples](https://github.com/robcarver17/systematictradingexamples) - 与《Systematic Trading》及[博客](http://qoppac.blogspot.com)相关的示例代码。
- [pysystemtrade_examples](https://github.com/robcarver17/pysystemtrade_examples) - 使用 pysystemtrade 的示例，Robert Carver 在其[博客](http://qoppac.blogspot.com)进行的扩展演示。
- [ML_Finance_Codes](https://github.com/mfrdixon/ML_Finance_Codes) - 《Machine Learning in Finance: From Theory to Practice》书籍相关的代码。
- [Hands-On Machine Learning for Algorithmic Trading](https://github.com/packtpublishing/hands-on-machine-learning-for-algorithmic-trading) - Packt 出版的《Hands-On Machine Learning for Algorithmic Trading》源码。
- [financialnoob-misc](https://github.com/financialnoob/misc) - @financialnoob 分享的研究笔记与代码。
- [MesoSim Options Trading Strategy Library](https://github.com/deltaray-io/strategy-library) - MesoSim 的免费且公开的期权交易策略库。
- [Quant-Finance-With-Python-Code](https://github.com/lingyixu/Quant-Finance-With-Python-Code) - Chris Kelliher 著《Quantitative Finance with Python》中的示例代码。
- [QuantFinanceTraining](https://github.com/JoaoJungblut/QuantFinanceTraining) - CQF（定量金融证书）培训期间执行的代码示例，按课程分类。
- [Statistical-Learning-based-Portfolio-Optimization](https://github.com/YannickKae/Statistical-Learning-based-Portfolio-Optimization) - 使用层次化平衡风险贡献（HERC）进行现代投资组合优化的 R Shiny 应用，基于 Raffinot (2018) 的研究。
- [book_irds3](https://github.com/attack68/book_irds3) - 《Pricing and Trading Interest Rate Derivatives》相关代码仓库。
- [Autoencoder-Asset-Pricing-Models](https://github.com/RichardS0268/Autoencoder-Asset-Pricing-Models) - 复现 [GKX, 2019](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3335536) 中的自动编码器资产定价模型。
- [Finance](https://github.com/shashankvemuri/Finance) - 包含 150+ 个与金融数据获取、处理与分析相关的 Python 程序。
- [101_formulaic_alphas](https://github.com/ram-ki/101_formulaic_alphas) - 使用 qstrader 实现 [101 formulaic alphas](https://arxiv.org/ftp/arxiv/papers/1601/1601.00991.pdf)。
- [Tidy Finance](https://www.tidy-finance.org/) - 一种进行金融实证研究的"整洁"思路，提供多语言（Python、R）的开源代码，方便学生和研究人员可重复实施财务研究项目。
- [RoughVolatilityWorkshop](https://github.com/jgatheral/RoughVolatilityWorkshop) - 2024 年 QuantMind 的 Rough Volatility 研讨会课程资料。
- [AFML](https://github.com/boyboi86/AFML) - 《Advances in Financial Machine Learning》一书所有练习题的完整答案（作者：Marcos Lopez de Prado）。

![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)

![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)



本文介绍了一个全新的量化投资框架，旨在借助**大型语言模型（LLMs）** 与**多代理（multi-agent）** 的协同来挖掘并优化股票投资策略中的Alpha因子。该方法从多模态金融数据（财报、价格、新闻、图表）中筛选并生成多元化的Alpha集合，再通过多代理机制根据实时市场情境动态评估与择优，从而构建适应性更强、表现更稳定的投资组合。

![](https://fastly.jsdelivr.net/gh/bucketio/img1@main/2025/02/01/1738398298801-307d1f4e-f7b3-498f-a775-a46708bb30c0.png)

---

## 1. Introduction

### 1.1 背景与动机

随着深度学习在金融交易预测方面取得显著进展，许多研究者与实务机构都在致力于开发更稳定、更可靠的量化投资模型。然而，当前模型往往在不同市场环境下表现不稳定，预测能力易受嘈杂数据或市场波动的影响。此外，在真实交易场景中，数据形式多样（文本、图表、财务指标等），如何更好地整合并提炼这些信息，成为量化投资的新挑战。

> 我们提出的**量化投资新框架**核心思想：  
> **(1) 大型语言模型（LLM）** 提供强大的信息抽取和推理能力，可自动生成和筛选出多样化的Alpha因子；  
> **(2) 多代理体系** 动态评估市场状况，从而在不断变化的环境中实现策略组合的自适应调权与组合。

### 1.2 Alpha挖掘面临的主要挑战

1. **传统方法的僵化**：许多Alpha挖掘基于启发式或经验规则，难以快速适应不同市场环境；  
2. **多源数据整合难度**：金融市场中信息不止于价格与财务指标，还包括新闻、社交媒体情绪、经济数据等；  
3. **市场变动**：表现优异的Alpha在另一种行情中可能失效，如何捕捉并跟随市场的结构性转变是关键。

### 1.3 框架概述

为应对上述挑战，我们提出了一个集LLM探索、多代理选择以及策略动态调权为一体的量化投资解决方案。  
- **LLM生成Alpha**：从学术论文、金融文献、财务数据、可视化图表等多模态信息中提炼种子Alpha因子；  
- **多代理体系**：在不同风险偏好和市场情景下对这些Alpha进行筛选、验证与信心评分；  
- **动态调权**：通过深度神经网络或其他学习方法，根据市场状态自动调整各Alpha在最终投资策略中的权重。

> 我们在中国A股市场（以上证50指数为例）进行了大量实证测试，结果显示在多项指标上均优于传统方法和其他SOTA基线。

---

## 2. Problem Formulation

### 2.1 Alpha因子与组合策略

在量化投资中，我们通常会对所选的n只股票，在每个交易日或交易区间内计算出多种Alpha因子。  

$\alpha = \sum_i w_i \alpha_i$ 

其中 $\alpha_i$ 表示某个种子Alpha因子，$w_i$ 是其在最终组合中的权重。我们的目标是通过历史回测与实时市场评估，找到满足当前市场情景的最佳因子组合并动态分配权重。

### 2.2 种子Alpha挖掘与选择

传统的Alpha挖掘常依赖静态规则或专家经验，难以面对突发环境变动或海量多源数据。为此，我们利用LLM的探索能力，结合多代理系统实现：  
1. **LLM自动生成**：从学术论文、财报、新闻等多模态数据中挖掘潜在公式化Alpha；  
2. **多代理严格评测**：进行回测和市场适配性测评，并给予信心评分\(\theta\)；  
3. **类别化**：将Alpha因子分为动量、均值回归、波动率、基本面等独立大类，便于进一步选择与组合。

### 2.3 Alpha公式化

我们要求LLM产出的种子Alpha具备“可解释的公式化形式”，包括：  
- **横截面运算**（如加法、对数等短期运算）；  
- **时序运算**（需要跨多日的数据，如移动平均、延迟等）。

---

## 3. Methodology

我们的整体方法可分为三个主要模块：**(1) 种子Alpha工厂（Seed Alphas Factory）**、**(2) 多模态多代理评估**、以及**(3) 动态权重优化**。

### 3.1 框架概览

1. **LLM种子Alpha工厂**：  
   - 通过大语言模型过滤和分类多模态文献、图表、财务数据，输出一批潜在的种子Alpha；  
   - 使用传统金融研究中的Alpha类别作为基础（如动量、均值回归等），保证因子间的相对独立性；  
2. **多代理决策**：  
   - 针对不同风险偏好与市场状态，在多模态数据环境下（文本、数值、图形、音视频等），由多个代理分别筛选出高置信度的Alpha；  
   - 每个代理会基于回测表现与对市场信息的解读，对因子赋予信心评分；  
3. **动态权重优化**：  
   - 最终通过深度学习（例如DNN）将这些候选Alpha结合起来，学习其对未来收益的预测能力；  
   - 根据市场状态持续调整权重，实现自适应的量化策略。

### 3.2 LLM过滤与分类

在第一阶段，我们为LLM（如ChatGPT的定制版“Alpha Grail”）提供多模态的研究文档，让其进行总结、分类并生成结构化的种子Alpha清单。  
- **文档多源**：学术论文、金融报告、图表、财报；  
- **分类结果**：例如Momentum、Mean Reversion、Volatility、Fundamental等；  
- **每类包含若干具体Alpha公式**：如动量因子的(CLOSE - DELAY(CLOSE,14))、波动率因子的STD(CLOSE, 20)等。

### 3.3 多模态多代理Alpha评估

在第二阶段，多代理系统会结合**文本、数值、可视化、音频/视频**等多模态数据，对这些种子Alpha进行评测：  
1. **风险偏好分析**：不同代理具有不同风险倾向（保守、中性、激进等），对Alpha的历史表现进行打分；  
2. **自适应回测**：在不同市场情形下模拟交易绩效，关注如信息系数（IC）、夏普比率等指标；  
3. **置信度评分**：每个代理对Alpha打出可信度分数，以过滤不可靠或表现欠佳的因子；  
4. **算法自动化选择**：通过设置信心度门槛，选出符合要求的Alpha子集。

### 3.4 Alpha权重优化

在第三阶段，我们采用DNN等深度学习模型，对上述选出的多个Alpha因子进行多维度拟合：  
- **输入**：Daily alpha值及股票历史价格；  
- **隐藏层**：可选用ReLU等激活函数，提升非线性拟合能力；  
- **输出**：预测下一期收益，并通过回测误差优化网络权重。  
最终得到一组最优的Alpha权重$\{w_i\}$，组合成一个动态、稳健的量化投资策略。


![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2025/02/01/1738398665334-a4af048e-a5a3-4692-9c67-39591fe961e3.png)


## 4. Experiment

我们主要在上证50指数范围内展开实证研究，验证以下几个核心问题：

1. **RQ1**：框架能否有效整合多模态信息并捕捉到不同市场环境下的新Alpha？  
2. **RQ2**：与传统Alpha生成或现有Alpha工厂相比，LLM驱动的挖掘方法在预测有效性上是否更佳？  
3. **RQ3**：整合信心评分与自适应策略后，整体表现能否在实盘测试中稳定跑赢市场指数？

### 4.1 数据集与设置

- **数据范围**：上证50成分股；  
- **特征**：基本行情（开高低收、量、VWAP）及公司财报等；  
- **时间划分**：训练集为2021.01.01-2022.12.31，测试集为2023.01.01-2023.12.31；  
- **评估指标**：信息系数（IC）、累计收益、回撤、夏普比率等。

### 4.2 主要实验结果

#### 4.2.1 RQ1：多模态与动态Alpha捕捉

- **多模态LLM分析**：结合文本（公司公告、新闻）、数值（财报）、图表（K线、交易量图）信息，在不同时间段（如2021.12.31-2022.09.30 vs. 2022.10.01-2023.xx.xx）自动获取适合当前市场的因子；  
- **实验案例**：  
  - **Case 1**：选出动量、成交量因子，如RSI、MACD、移动平均等；  
  - **Case 2**：强调波动率与经济因子，如ATR、Bollinger Band宽度、毛利、经营收入比等。  
  - 结果显示，框架能针对市场情形差异灵活选出更相关的Alpha集合。

#### 4.2.2 RQ2：LLM挖掘的Alpha是否更具预测价值

我们通过IC来衡量所选Alpha与实际未来回报的相关性。对比现有的“传统Alpha工厂”，LLM框架在动量、波动率和基本面方面的平均IC明显提升，说明预测有效性更强。

#### 4.2.3 RQ3：组合策略能否跑赢市场

我们选择了Top-k/Drop-n策略，每日买入k只Alpha值最高的股票，并限制一次性调仓不超过n只。  
- **回测区间**：2023年；  
- **结果**：  
  - 我们框架的累计收益率可达 **+53.17%**，同期上证50指数下跌 **-11.73%**，其他基准基金收益也明显落后；  
  - 回测证明，此LLM驱动+信心评分+动态调权的策略能在大部分市场阶段 outperform 市场。

---

## 5. Related Work

1. **公式化Alpha**：传统上，Alpha因子基于基因编程、遗传算法或机器学习模型（如XGBoost、LSTM），但缺乏在多模态信息场景下的动态适应能力；  
2. **金融领域LLM**：尽管通用大模型研究火热，但在金融任务（尤其是量化交易）中仍属早期探索，许多研究集中在财务文本摘要或新闻情绪分析；  
3. **多模态与多代理**：最新文献指出，结合文本、图像、数值等不同数据形式，可以显著提高对市场情绪与波动的捕捉能力。此外，使用多代理协同决策，可模拟不同策略与偏好，提高整体的稳健性。

---

## 6. Conclusion

本文提出了一个将**大型语言模型**与**多代理架构**相结合的新方法，用于量化股票投资与Alpha挖掘。我们通过LLM从多模态金融信息中生成并筛选多元化的Alpha因子，再借助多代理结构对实时市场环境做出动态评估与调权，从而在不同市场状态下均能保持较高的投资回报与策略稳健性。实证结果表明，该框架在中国A股市场多项指标上均显著优于传统方法，为AI驱动量化策略树立了新基准。

> **未来展望**：  
> - 考虑将多代理升级为专家混合（Mixture of Experts）的模型，提高学习效率；  
> - 引入金融知识图谱，进一步增强对行业、公司关系等隐性信息的理解；  
> - 推广应用至能源预测、异常检测、医疗等其他时序预测领域，拓展公式化Alpha思路的通用性。

---

## 参考文献（节选）

1. Fama, E.F. *Efficient capital markets: A review of theory and empirical work.* Journal of Finance, 1970.  
2. Cui, C., Wang, W., Zhang, M., et al. *Alphaevolve: A learning framework to discover novel alphas in quantitative investment.* SIGMOD, 2021.  
3. …（更多文献请参见原文）

详见附录中各Alpha因子列表、数据集与实验设置说明，以及所使用的LLM提示模板等细节。  


## 关于LLMQuant

LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。欢迎加入**知识星球**获取**内部资料**。


![](https://fastly.jsdelivr.net/gh/bucketio/img13@main/2025/01/29/1738120164157-32d67f79-b006-4f4e-bb7b-13db27759e2b.JPG)


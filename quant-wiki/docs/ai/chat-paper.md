![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)

# 2025还在手写论文？教你用ChatGPT自动生成股票研究的学术论文

![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

2025年会是人工智能广泛应用到金融和量化的一年，LLMQuant教你用ChatGPT自动生成研究股票的学术论文，本篇科普将基于 **Novy-Marx & Velikov (2024)** 的论文“AI-Powered (Finance) Scholarship”展开，介绍如何利用大型语言模型（LLMs）实现自动化的学术研究生产，包括从数据挖掘到论文写作的全流程。本文还将结合少量数学公式，并讨论该技术在金融研究，尤其是股票收益预测中的应用与潜在影响。

## 1. 背景与动机

### 1.1 大型语言模型与学术研究

在学术研究领域，传统的“研究-理论-投稿”流程往往需要耗费研究者大量的时间和精力来探索数据、提出假说、撰写论文和回应审稿意见。随着 **ChatGPT、Claude** 等大型语言模型的崛起，AI 正在改变这一流程：

1. **自动生成研究创意**：利用 LLMs 的“生成式”能力，可以从海量文献或数据中挖掘潜在研究思路。  
2. **加速论文写作**：LLMs 可以根据特定提示（Prompt）快速生成论文初稿、摘要以及其他辅助性文字材料。  
3. **数据挖掘与结果解释**：从大规模数据中“筛选”出有效的信号后，LLMs 还能“自动化”地为这些信号匹配合理的经济理论解释。

然而，这种自动化进程也带来了新的挑战，如 **HARKing**（Hypothesizing After Results are Known，先看结果再编假说）在大规模层面的“工业化”风险。文章强调了技术本身既可能“提升研究效率”，也可能“滥用”。

### 1.2 股票收益预测与“异象”研究

在金融学中，寻找能够预测横截面股票收益的“信号”或“因子”是个经典而重要的课题。自从 Fama-French 提出的三因子、五因子模型以后，又陆续有数百甚至上千个“异象”（anomalies）被学术界发现。

- **什么是“异象”**：指在经典有效市场假说（EMH）框架下无法轻易用传统风险因子解释的一种显著的超额收益规律。  
- **常见手段**：通过对财务报表(如资产负债表、利润表等)中的会计科目进行各类组合（如比率、差分等），然后用组合或回归的形式观察这些信号对未来收益的预测性。

由于海量可能的信号会带来 **多重检验**（multiple testing）和 **数据挖掘**（data snooping）的风险，传统研究需要严格的统计方法来降低虚假发现率，例如对截面回归做 Fama-MacBeth 调整，对多因子进行 FDR（False Discovery Rate）校正等。**Novy-Marx & Velikov (2024)** 则提出了被称为 **“Assaying Anomalies”** 的系统化方法，对挖掘到的因子进行逐层过滤和评估。

---

## 2. 自动化研究流程概览

论文介绍了一条完整的“自动化研究生产线”，涵盖：

1. **大规模信号挖掘**  
2. **统计显著性与稳健性检验**  
3. **使用 LLMs 自动生成研究假说、命名信号、撰写完整论文**  

下面将对各环节做详细介绍，并插入部分数学公式以加深理解。

---

### 2.1 大规模信号挖掘

研究首先从 **COMPUSTAT** 数据库中提取了 **31,460** 个潜在的会计信号，每个信号都来自不同会计科目的组合或差分。例如：

- **比率型信号**：  
  $$
  \text{Signal}_{i,t} = \frac{\text{AccountingVar1}_{i,t}}{\text{AccountingVar2}_{i,t}}
  $$

- **差分型信号**：  
  $$
  \text{Signal}_{i,t} = \frac{\text{AccountingVar1}_{i,t} - \text{AccountingVar1}_{i,t-1}}{\text{AccountingVar2}_{i,t-1}}
  $$

这里 \(i\) 表示公司，\(t\) 表示时间（通常为年或季度）。随后，为了确保数据质量，研究对这 31,460 个初始信号进行过滤，如去重、排除数据缺失严重的项目等，最终剩下 **17,074** 个候选信号。

---

### 2.2 统计显著性与稳健性检验

为了避免出现大量“假阳性”信号，研究采用多重方法对每个信号进行考察：

1. **分组排序检验（Portfolio Sorts）**  
   - 按照信号值从小到大进行分组（如 5 分组、10 分组），考察不同组别在未来收益上的表现差异。  
   - 用传统的 **t-统计量** 来判断分组间收益差异是否显著：  
     $$
     t = \frac{\overline{r}_\text{High} - \overline{r}_\text{Low}}{\sqrt{\mathrm{Var}\bigl(\overline{r}_\text{High} - \overline{r}_\text{Low}\bigr)}}
     $$  

2. **因子模型回归**  
   - 对超额收益做多因子回归（例如 Fama-French 6 因子模型），判断在控制已有风险因子后，信号是否仍具有统计意义：  
     $$
     r_{i,t} - r_{f,t}
     = \alpha
       + \beta_1 \cdot \text{MKT}_{t}
       + \beta_2 \cdot \text{SMB}_{t}
       + \cdots
       + \beta_6 \cdot \text{Factor6}_{t}
       + \epsilon_{i,t}
     $$  
   - 若估计得到的 \(\alpha\) 依旧显著为正或负，则表明信号具有额外信息含量。

3. **“Assaying Anomalies” 协议**  
   - **Novy-Marx & Velikov (2024)** 推出了一个系统化的“化验”流程，将每个信号与文献中已有数百个异象进行对比，看其在一系列严苛的稳健性和与“相似信号”区分度测试中能否胜出。

最终，能通过全部测试的信号非常少，仅剩 **96** 个。这些信号被认为是真正有潜力在横截面收益中发挥预测作用。

---

### 2.3 使用 LLMs 自动生成论文

在完成实证分析后，研究者以这 96 个“幸存”信号为基础，用 **GPT-3.5-turbo** 或 **Claude 3.5-Sonnet** 等 LLMs 自动撰写论文。主要包括：

1. **命名系统**  
   - 提示 LLM 生成学术化的“信号名称”，如 *Operating Liquidity Margin*、*Tax Efficiency* 等，避免使用过于通用的“ratio”或“difference”字样。  
2. **论文主体生成**  
   - 按照预先设计的提示（prompts），LLMs 会生成包括**摘要、引言、数据方法、结果和结论**在内的完整论文文本。  
   - 引言部分会“自动捏造”或引用部分真实文献来构建理论动机，并进行适度的修饰。  
3. **大规模批量产出**  
   - 针对每个信号，自动生成 3 个版本的完整论文（换不同角度的理论解释），合计 **288 篇**。只需极短时间便可完成，相当于传统研究模式下不可想象的“论文流水线”。

此举在展示 AI 潜能的同时，也强调了其可能带来的风险，如 **“工业化 HARKing”**，即在看到显著结果后再编写各种合理化解释，忽视了对真实经济机理的追求。

---

## 3. 关键发现与影响

### 3.1 效率与规模

- **极度高效**：传统学术论文往往需要数月乃至数年的酝酿和写作，而通过 AI 自动化，只要数据挖掘与统计分析流程已完成，生成阶段只需数分钟即可产出大批量的“学术论文”。  
- **潜在滥用**：如果没有额外的质量监督，这种方法会让低质量或重复性极高的论文泛滥，严重冲击传统的同行评审体系。

### 3.2 研究者声誉与同行评审

- **声誉机制**：金融学或其他学科的研究者往往依赖于持续稳定的高水平研究来建立学术影响力。**短期靠量取胜**或许无法获得真正的学术地位。  
- **同行评审的压力**：当大量“自动化论文”涌入期刊时，审稿与编辑同样面临巨大的鉴别负担。如何快速判断论文质量，如何防止“造假引用”或“自引互引”成为新问题。

### 3.3 “假说-实证”范式的演变

- 在传统范式里，研究者常先提出基于经济学原理的假说，再进行数据检验。现在的流程中，“先挖数据发现规律，再由 AI 创造假说”越来越盛行。  
- **这是否真正违背科学方法？** 实际上，历史上许多重要发现也并非先有完备理论后再检验。真正的问题是：**如何确保后验假说不只是“事后诸葛”，而具备可拓展或可预测的能力。**

---

## 4. 数学示例：如何评估“自动假说”的合理性

假设我们在传统“Fama-MacBeth”回归框架下评估一个自动生成的信号
$$
\text{SIGNAL}_{i,t-1}
$$
对下一期收益
$$
r_{i,t}
$$
的影响：

$$
r_{i,t}
= \alpha_t
+ \beta_t \cdot \text{SIGNAL}_{i,t-1}
+ \gamma_t \cdot \mathbf{X}_{i,t-1}
+ \epsilon_{i,t},
$$

其中
$$
\mathbf{X}_{i,t-1}
$$
是一组控制变量或其他经典因子（如 SIZE、BM、MOM、RMW、CMA 等）。  

1. **截面回归**：针对每个时期 \(t\) 做截面回归，得到一系列

$$
\hat{\beta}_t
$$。  
2. **时间序列均值与方差**：  
   $$
   \overline{\beta} = \frac{1}{T}\sum_{t=1}^{T} \hat{\beta}*t,
   \quad
   SE(\overline{\beta}) = \sqrt{\frac{1}{T(T-1)}\sum*{t=1}^{T} \Bigl(\hat{\beta}_t - \overline{\beta}\Bigr)^2}.
   $$

若最终
$$
\overline{\beta}
$$
显著大于零且 t-统计量也显著，则可认为该信号对横截面收益有预测能力。

然而，如果
$$
\text{SIGNAL}_{i,t-1}
$$
是从数万变量里盲目数据挖掘得到的，理论上应当做多重检验修正；否则，我们可能低估了假阳性概率。这一点在 AI 大规模自动化产出论文中显得更为突出，故必须对 **统计显著** 与 **经济解释** 进行双重审查。

---

## 5. 风险与前景

1. **学术诚信**：  
   - 大规模自动生成论文可能导致**大面积“虚构文献”**或**自引操作**，影响学术界对质量与原创性的评估。  
   - 需要建立**引用识别与验证系统**，类似于查重工具或数据库校验，以减少 LLM “幻觉型引用”（hallucination）的危害。

2. **同行评审改革**：  
   - 或许需要更加明确的**审稿标准**，例如要求作者（或 AI）在**提交论文时自动附上可检索的数据和代码**，以便快速复制和验证研究结果。  
   - 提高对**经济机制**创新的要求，而不只是满足于统计意义。

3. **未来研究方向**：  
   - 随着 **LLMs** 进一步迭代，研究者可整合**自动验证模块**，如自动化核对参考文献真伪、自动判断理论的一致性。  
   - 开发针对金融学研究的“AI 实验室”——提供全套从大数据清洗、因子检测、到论文格式化的“一站式”服务，但需更严格的监管和评审机制。

---

## 6. 结论

**Novy-Marx & Velikov (2024)** 的研究展示了 AI 技术在金融学研究中的新图景：  
- 通过大规模挖掘会计数据，可以在短时间内筛选上万条候选信号；  
- 利用新的检验协议和多因子模型进行“去伪存真”；  
- 最终将“幸存”的信号交由 LLM 大规模生成“学术化论文”，包括抽象的经济理论动机与范式。

在为学术研究带来**质的飞跃**的同时，也对**学术诚信**、**研究质量**和**同行评审**提出了新的严峻挑战。机器可以帮助我们更快地“发现”并“解释”异象，但唯有人类学术共同体才能决定哪些发现和解释真正推动了金融学知识前沿的发展。

未来，学术界或许需要构建一套新标准，以平衡研究效率与学术严谨。在这个转型的关键时刻，我们更需要明确原则：**在拥抱技术的同时，不能放弃对学术真理和研究质量的坚守**。

---

## 参考文献（示例）

- Chen, A. Y., Lopez-Lira, A. & Zimmermann, T. (2024). *Does peer-reviewed research help predict stock returns? Working Paper.*  
- Fama, E. F. & French, K. R. (2018). *Choosing factors.* Journal of Financial Economics, 128(2), 234–252.  
- Kerr, N. L. (1998). *HARKing: Hypothesizing After the Results are Known.* Personality and Social Psychology Review, 2(3), 196–217.  
- Novy-Marx, R. & Velikov, M. (2024). *Assaying Anomalies.* Working Paper.  
- Yan, X. & Zheng, L. (2017). *Fundamental analysis and the cross-section of stock returns: A data-mining approach.* The Review of Financial Studies, 30(4), 1382–1423.  

如需了解更多细节，获取论文原文和**代码**，欢迎加入**LLMQuant知识星球**。

## 关于LLMQuant

LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。

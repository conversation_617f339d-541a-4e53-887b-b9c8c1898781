![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)

# 量化交易员带你入门：如何打造“好用”的交易策略？

![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

在本文中，我想和大家探讨一个很核心的问题——**什么样的交易规则才算好？** 这里主要面向依赖**量化交易** 的读者。

---

## 1. 交易规则从何而来：两种思路

### “先数据” vs. “先想法”

系统化交易基于一个前提：**未来将与过去相似**。我们希望在历史数据里找到行之有效的规则，并期待它们在将来也能奏效。不过，寻找历史上曾经赚钱的规则有两种常见做法：

1. **先数据（data first）**：直接在历史数据中“挖掘（data mining）”出一些看似 profitable 的模式，然后加以利用。  
2. **先想法（ideas first）**：先提出一个具有逻辑或理论支撑的假设，再用数据来验证它是否真正有效。

> “我认为外汇汇率主要受利率影响，所以我先假设‘利率差驱动汇率变动’，再用数据来检验它。”  
> 这是“先想法”的典型例子；正如 Systematica 对冲基金负责人 Leda Braga 所言，她们会先有一个假说，然后再进行数据分析，而不是盲目在数据里找灵感。

#### 哪种方法更好？

| **先想法**                                                                              | **先数据**                                                                                                               |
| --------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------ |
| - 如果思路正确，往往不需要进行大量、繁杂的“拟合”操作。                                  | - 在“先想法”中，可能会不自觉地只去验证“市场盛传”的规则，这本身也可能导致过度拟合。                                        |
| - 因为测试空间相对有限，过度拟合的风险较小。                                            | - 在“先想法”下，人们可能不断尝试新想法，直到找到能赚到钱的那一个，这也属于另一种形式的过度拟合。                         |
| - 规则通常更简单、易理解，背后自带“故事”。                                              | - “先数据”中所有的拟合都是显性的，更容易控制过度拟合的程度。                                                            |
| - 若有一套合理解释，通常更容易明确其盈利来源。                                          | - 一个好故事并不能保证重复盈利，可能给你一种“错误的安全感”（叙事偏差）。                                                |
| - 更适合想清楚利润从何而来的研究者和交易者。                                            | - “先数据”也可能挖掘出一些意想不到的新模式，从而带来突破性的“黑科技”策略。                                             |

> 引用：我个人更喜欢“先想法”的方式，因为它能让我更清楚地知道自己为什么会赚钱，也更容易避免无意义的过度拟合。另一方面，在某些高频交易领域，市场结构变化快、数据量又大，“先数据”反而可能更适用。

---

![](https://fastly.jsdelivr.net/gh/bucketio/img15@main/2025/01/22/1737589546535-0f621a57-2c2f-492d-ae3d-d9ef8b0a98d1.png)

## 2. 为什么要理解交易规则的盈利来源

如果我们知道某条规则为什么能在过去赚钱，就可以大致推测它在未来是否还能赚钱，以及何时会失效。

- **先想法**：只要想法本身包含盈利原因（例如“人们因为认知偏差而不愿及时止损”），就能给出直观解释。  
- **先数据**：则需要先找到盈利规则，再去倒推背后的原因。复杂的数据驱动策略往往很难阐明为什么会有效。

> 引用：需要警惕“叙事偏差（narrative fallacy）”。人类喜欢“听起来有道理的故事”，但并不代表这条规则经得起统计检验。

如果我想完全信任某条交易规则，希望它既有一个扎实的数据回测，也有能自圆其说的故事，两者缺一不可。

---

## 3. 让人“一看就懂”的策略更靠谱

有时，我们需要在实际操作中快速判断一条策略的行为是否“符合预期”。比如在企业财报前夕，如果联合利华股价启动上涨，那么一个趋势跟随系统大概率会做多。如果看到它反而做空，你就得怀疑是不是数据错误或软件 bug。

但若规则非常复杂，可能会因为某些小细节而在某些时刻做出让人难以理解的决策。这不仅难以排查问题，也增加了决策的不确定性。

---

![](https://fastly.jsdelivr.net/gh/bucketio/img15@main/2025/01/22/1737589435423-6f14c563-a0ad-411d-a7c0-0e0c15e43788.png)

## 4. 尽量保持规则的“简单”

我尤其偏好那些设计简洁、逻辑清晰的规则。它们的**可解释性**相对较强，也更不容易陷入因为“不断加参数”而导致的过度拟合中。

- **先想法**：只要你的想法本身不太复杂，形成的规则往往也会保持简洁。  
- **先数据**：则很容易因为想追求更高收益而添加大量参数，结果导致策略十分庞杂，既难解释，也更容易失效。

当然，复杂并不一定不好，一些先进的数据挖掘手段能挖到“简单规则”发现不了的奥秘。只是这类策略需要更多时间来验证其稳定性与适用范围。

---

## 5. 能否系统化？

如果一条策略无法写成可量化、可编程的形式，就很难真正成为**系统化交易**的一部分。

1. **过于主观**：某些分析方法过于依赖个人经验和直觉，难以“客观量化”。比如极为复杂的蜡烛图形态、或者并购套利时对监管、法律因素的定性判断，很难完整转化为严谨的算法。  
2. **数据限制**：有些策略需要的信息并不公开或难以收集，比如并购案例中涉及的法律细节。如果数据不完整或严重不足，也就无法进行有效的回测。

另外，有些新兴数据（如 Twitter 热度、Google 搜索指数）听起来很酷，但缺乏足够长的历史窗口来验证，容易陷入噱头大于实效的困境。

---

## 总结

- **先想法**和**先数据**是构建系统化交易规则的两条主要路径，关键在于避免过度拟合，理解利润来源。  
- **能解释自己的盈利逻辑**，并且在回测和现实中都表现相对稳定的交易规则，往往更值得信赖。  
- **简单易懂的策略**更方便管理和排错，也通常更具可解释性。  
- **数据或信息的可获得性**决定了是否能够将策略系统化；如果缺乏高质量数据，或策略严重依赖主观判断，可能只能半自动乃至纯手动进行。

> 引用：  
>
> - 在高频交易等领域，“先数据”可能更有效，因为市场结构的变化速度快，而且数据量充足，能支撑频繁的重新拟合和模型迭代。  
> - 在“想法驱动”下，我们更容易构建简单、直观的策略，也能深度思考为什么它能赚到钱，从而对未来有更理性的期待。

以上就是我对“如何评判一个交易规则是否优秀”的一些思考。希望能帮到大家在构建或评估策略时拥有更清晰的思路，少走弯路。

## 关于LLMQuant

LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。

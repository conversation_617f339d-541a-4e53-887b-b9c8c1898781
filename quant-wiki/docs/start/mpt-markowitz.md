![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)

# 量化分析开山鼻祖：一文读懂现代资产组合理论与马可维茨模型

![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

在投资与理财的世界里，有一句常被挂在嘴边的话——“世上没有免费的午餐”。换句话说，所有的收益背后都隐藏着风险，而这对我们每个想在金融市场中分一杯羹的人来说，都是再清晰不过的事实。那么，问题来了：我们如何量化这种风险与回报的关系？又有没有一种方法，能在不承担过高风险的前提下，让我们的投资组合获得更理想的回报呢？今天，我想和大家聊一聊**现代资产组合理论（Modern Portfolio Theory，简称MPT）** 以及由哈里·马可维茨（Harry Markowitz）提出的**马可维茨模型（Markowitz Model）**。

也许在读这篇文章前，你对“风险管理”或“资产配置”这些概念并不陌生，但如果能深入了解MPT，你可能会彻底改变对风险的看法，甚至将这一理念融入日常生活中的决策。

## MPT的诞生：风险、回报与诺奖荣耀

上世纪50年代，哈里·马可维茨率先提出了MPT的核心思想。他认为，对任何资产来说，都存在着风险与报酬间的权衡关系。如果用资产的价格波动幅度（即波动率）来度量风险，那么价格越不稳定的资产，其所伴随的风险越高，回报潜力也相对越大；而稳定性更高的资产，虽然风险低，但回报的上限也相对较低。

![](https://fastly.jsdelivr.net/gh/bucketio/img4@main/2024/12/14/1734215702884-60a6995b-8829-4cc9-8cf9-0c834f77053b.png)

在这套理论体系中，风险与回报不再是模糊的概念，而是可以用数学进行描述和优化的对象。马可维茨本人也因此获颁**诺贝尔经济学奖**，这既是对他个人智慧的肯定，也是对MPT在金融学界地位的认可。

## 相关性与协方差矩阵：理解资产间的互动

MPT的精妙之处在于，它不仅仅关注单个资产的风险与回报，还强调不同资产之间的互动关系。在现实市场中，有些资产会同时上涨或下跌（正相关），有些则可能一涨一跌（负相关），还有一些基本互不相关。

这种资产之间的互动关系可以用协方差（Covariance）来衡量。当我们将各资产的协方差值组织成一个矩阵时，就得到了著名的协方差矩阵，用符号$$\Sigma$$（Sigma）表示。这个$$\Sigma$$就像一张投资组合的全景图，展示了各个资产间的波动关系。正是通过理解这些协方差值，我们才能更清楚地认识到选择那些彼此相关性较低的资产有多么重要。

## 有效前沿（Efficient Frontier）：找到理想组合的关键

即便我们理解了风险和相关性，仍有一个难题：如何为每种资产分配合适的投资比例？毕竟理论上组合的权重搭配无穷无尽。但马可维茨指出，在这无数可能性中，只有一条特定的曲线——“有效前沿”——代表着最优的风险-回报组合。

**有效前沿**指的是：在给定的风险水平下实现最高期望回报，或在给定的回报要求下实现最低风险的所有组合点。这意味着，如果你的投资组合位于这条前沿上，那么在相同条件下再也找不出更优的配置方案了。

## 数学基础：从矩阵到最优解

让我们稍微深入数学层面，不过不必担心被公式吓到，理解原理即可。

1. 用一个权重向量$$w$$表示各资产在组合中的比例。
2. 协方差矩阵为$$\Sigma$$。
3. 组合的方差（即风险的量化指标）可通过$$w^T \Sigma w$$计算，其中$$w^T$$是$$w$$的转置。方差的平方根即为组合波动率。
4. 若有预期回报率向量$$\mu$$，则组合的期望回报率$$R$$为$$\mu^T w$$。

我们的目标是：在尽可能低的风险下（最小化$$w^T \Sigma w$$）获得尽可能高的回报（最大化$$\mu^T w$$）。在数学求解中，由于目标是“双重”且相互制约的，我们会得到一条曲线解（即有效前沿），而非单一点解。

最终的权重公式可写为：

$$ w = \lambda \Sigma^{-1} \mu $$

这里的$$\lambda$$是投资者的风险容忍度参数，从0（极度保守）到无穷大（极度冒险）都有可能。通过改变$$\lambda$$，我们可以在有效前沿上选取不同的点。

## 多样化的力量：为什么不全押比特币或可口可乐？

试想如果你把所有钱都投在某一种资产上，比如只买比特币，或者只买可口可乐的股票，会有什么问题？比特币波动巨大，可能让你在短期内坐上“过山车”，而只买可口可乐虽然稳健，却难以获得更高增长空间。MPT告诉我们：不必极端，你可以通过持有多种低相关性、期望回报为正的资产来分散风险，从而在总体风险与回报之间找到更优解。

这种思维同样适用于新兴资产，例如加密货币。如果你想配置一些比特币，却又不想全盘押注，那么以MPT为指导的组合配置或许能为你提供方向。

![Harry Markowitz](https://fastly.jsdelivr.net/gh/bucketio/img15@main/2024/12/14/1734215736417-d72c0d15-f926-4249-a867-91bc37e7e91f.png)

## 预期回报的估算与CAPM模型

上述讨论中，预期回报率$$\mu$$看起来是已知的，但实际上，这是一项颇具挑战性的工作。你需要根据自己的经验、研究和模型来决定各资产的预期回报。

许多投资者参考“资本资产定价模型”（Capital Asset Pricing Model，CAPM）来帮助他们给资产定价。CAPM的公式如下：

$$ \text{资产预期回报} = R_f + \beta (R_m - R_f) $$

其中：

- $$R_f$$：无风险利率（常用国债收益率表示，数据可从官方渠道获取）。
- $$R_m$$：市场期望回报率，可用历史数据或模型预测。
- $$\beta$$：衡量资产对市场波动敏感度的指标，定义为资产与基准的协方差除以基准的方差。

借由CAPM，你可为单个资产得到一个相对“合理”的预期回报，把这个值作为$$\mu$$中的元素，从而为投资组合优化铺路。

## 总结与行动建议

读到这里，你或许已意识到，MPT不是一招制敌的灵丹妙药，而是让我们在投资决策中更加理性化和数据化的工具。它让我们用可量化的方式权衡风险与报酬，并通过数学手段寻找最优配置。当然，没有什么模型能保证你稳赚不赔，但MPT为我们提供了一个框架，让我们明白自己在为预期回报承担怎样的风险。

**提醒，这篇文章的内容并非投资建议。投资永远存在不确定性，过去表现不代表未来。**

## 关于LLMQuant

LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。欢迎加入**知识星球**获取**内部资料**。

![](https://fastly.jsdelivr.net/gh/bucketio/img6@main/2024/12/09/1733785266624-664ccf80-86b8-4dc3-bd9c-81f485e6e0cf.JPG)

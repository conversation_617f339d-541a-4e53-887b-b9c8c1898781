![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)

# 打造完美量化简历
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

## 申请量化金融职位和实习时，简历中应包含哪些关键词和细节

量化分析师在求职和实习申请中常面临的一个问题是：简历该如何撰写？鉴于这些职位的竞争激烈程度，优化简历无疑是赢得面试机会的关键。如今，众多公司采用ATS（申请人跟踪系统）来筛选简历，通过关键词进行初步筛选。在这一阶段，正确的关键词和细节将助你顺利过关。

本文将为你提供申请各类量化金融职位时简历应包含的通用指南。此外，我们还将分享一项深入分析的结果，该分析基于超过500份量化职位描述，揭示了量化雇主普遍寻求的资质。

## 通用指南

在撰写量化交易员或量化研究员职位的简历时，应着重展示你在统计学和数学方面的能力。这些技能不仅是面试中的评估重点，也是工作中最常依赖的技能。值得提及的内容包括你曾参与的数学竞赛（如AMC、Putnam等）及其成绩，学术标准化考试的高分，你参与的数据分析项目（尤其是使用Python的项目），以及你完成的统计学课程。
另一方面，如果你申请的是量化开发者的职位，你需要重点突出你在计算机科学领域的知识。无论是编译器还是分布式系统，你都应该展示你在这些领域参与的项目。一些不错的项目例子包括构建一个模拟市场交易所或用Python开发深度学习模型。

## 关键词优化

虽然上述建议是通用的最佳实践，但我们更进一步，分析了量化职位描述中最常见的关键词。针对每种职位类型（量化开发者、研究员和交易员），我们统计了关键词在职位描述中出现的频率。研究结果如下。

从这项分析中，我们可以得出一些有趣的结论，以下是其中几个值得关注的要点。

1. 拥有博士学位并不是成为量化研究员的唯一条件。人们往往高估了博士学位的重要性，但实际上，只有三分之一的量化研究职位明确要求博士学位。

2. 如果你不确定为了成为量化分析师应该选择什么专业，可以参考以下建议：
1. 量化开发者 - 计算机科学
2. 量化研究员 - 统计学
3. 量化交易员 - 数学
3. Python是所有量化职位中最受青睐的技能，因此学习并在简历中提到它至关重要。Excel也是一个经常被提及的技能，将其纳入简历也会对你有所帮助。

## 总结
感谢您阅读我们的文章，希望它能为您的量化简历撰写提供有益指导。 

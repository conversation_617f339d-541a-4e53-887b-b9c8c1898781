![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)

# 量化面试官的两种类型
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

## 如何通过了解面试官来准备量化金融面试

在与潜在的量化公司面试时，你通常会经历**多轮面试**，期间你会遇到公司内的多位量化专家。由于量化公司招聘的人才背景多样，每位专家可能在自己的研究领域内有独到见解，他们提出的问题往往反映出他们最熟悉的概念和主题。例如，一位在学术界研究物理学五年的量化研究员，比起金融中的特定主题如二项式定价模型，更可能问你一个与随机过程动态相关的问题。

总的来说，在面试过程中，你会遇到两种主要的量化面试官类型。这两种类型是：

1. **纯数学家**
2. **应用数学家**

在本文的后续部分，我们将详细介绍这两种类型的区别以及他们各自可能提出的问题类型。需要注意的是，为了充分利用这些信息，你必须首先能够识别出你将由哪种类型的量化专家进行面试。通常情况下，面试官的名字会在面试前告知你，这时你可以通过LinkedIn快速搜索到他们的相关信息。此外，根据你面试的量化公司类型，你可以对在那里工作的量化专家类型有一个大致的了解。

## 纯数学家

纯数学家量化专家在概率论和统计学方面具有深厚的专业知识。这些量化专家通常拥有金融或统计学的博士学位，因此他们的面试问题往往偏向理论性和证明导向。在技术面试中，你可能会遇到以下主题的编程和统计问题：

1. 蒙特卡洛方法
2. 布莱克-斯科尔斯模型
3. 测度论
4. 随机过程
5. 贝叶斯方法

## 应用数学家

应用数学家量化专家来自数学、金融或统计学以外的背景。这些量化专家通常拥有物理学、工业工程或计算机科学等领域的学位。他们在数值分析和线性规划方面知识更丰富，因此会在这方面对你进行评估。可能涉及的主题包括：

1. 优化方法
2. 微分方程
3. C++编程
4. 有限差分方法
5. 机器学习

## 如何让面试官对你印象深刻

虽然许多人认为面试是在效率前沿上运作的，但这远非事实。人为偏见在决定你是否进入下一轮时起着重要作用，因此与其忽视它，不如善加利用。

一个有效的方法是提前研究你的面试官。了解他们的背景、兴趣和档案，并围绕他们感兴趣的话题来组织你的问题。例如，如果你的面试官有研究背景，可以询问他们发表的论文或他们的学术经历如何塑造了他们对量化金融的理解。

相反，如果你的面试官之前曾在其他公司工作过，不妨问问他们那段经历，以及与他们现在的工作有何不同。这类问题会让面试官对你印象加分，在决定谁进入下一轮时，这可能会为你赢得优势。

![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)
# 内部课程｜剑桥大学LLMQuant和北大量化交易协会内部培训项目全面启动
![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

剑桥大学LLMQuant和北京大学量化交易协会**联合举办**的内部培训项目现已启动！本次内培旨在帮助对量化交易、机器学习、以及衍生品感兴趣的同学深入学习与实践。无论你是刚接触量化交易的新手，还是有丰富经验的研究者，这里都有适合你的培训内容！

## 关于LLMQuant

LLMQuant起源于剑桥大学校内，是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Jump Trading、Man Group、国内顶尖私募等一流企业。

## 内培课程合作伙伴

![](https://fastly.jsdelivr.net/gh/bucketio/img14@main/2024/09/04/1725457918602-9850222e-1dce-4d0a-98db-f5783681eb37.jpg)

北京大学量化交易协会(QTA)是一个以量化交易为主题的学术型社团，也是北京大学校内最有影响力的量化社团之一。

## 提供支持

![](https://fastly.jsdelivr.net/gh/bucketio/img0@main/2024/09/04/1725457918597-d07a6531-b5a1-4bd9-8568-0e4ea34d0701.png)

剑桥大学算法交易协会(CUATS) 是剑桥校内最有影响力的量化交易社团，合作伙伴包括Jane Street、Citadel、Optiver、SIG、DRW等。

## 项目亮点

我们将从**多因子模型**、**机器学习**、**衍生品**等多个方向，深入探讨量化交易中的关键理论与应用。每个模块都将由经验丰富的导师团队带领，通过**理论学习**与**实战项目**相结合的方式，帮助你在最短时间内获得最大提升，项目最后提供三个来自**业界导师**的实践项目，助力参与同学了解量化工作中的日常工作。

## 多因子模型方向

### 内容简介

多因子模型是量化交易研究的基础。本模块的主要目标是帮助社区成员建立对市场结构的深入理解，熟悉多因子研究的流程，并自主设计和优化因子。

### 课程内容

1. **理论学习**：阅读《因子投资》、最新研报与学术论文，构建对多因子模型的整体认识。
2. **实践研究**：掌握因子研究全流程，涵盖因子复现、构建与改进。导师将提供具体的研究课题，帮助你挖掘高表现因子。

无论你是初学者还是进阶研究者，我们的导师团队都将帮助你快速成长，深入因子研究领域。

## 机器学习方向

### 内容简介

本模块聚焦机器学习在量化交易中的应用，结合最新的机器学习技术与金融数据分析，带你探索机器学习在非结构化数据处理中的潜力。

# 课程内容

### 1. 机器学习配对交易策略

该策略通过使用机器学习将具有相似股票回报和公司特征的股票配对，改进了传统的配对交易。通过发现这些股票对，并押注于均值回归，策略在股票偏离历史关系时获利。使用凝聚聚类法找到最佳的股票对，并每月重新平衡。

- **资产**：NYSE、AMEX、NASDAQ 上市普通股
- **交易逻辑**：基于具有相似特征和过去回报的股票群体，做多表现不佳的股票，做空表现优异的股票。
- **基本原理**：历史上有共同走势的股票预计在偏离时会回归均值。结合公司特征能够提高股票对的选择，从而提高收益。
- **平衡周期**：每月

### 2. 通过机器学习预测股票收益

该策略不依赖单一的收益估计，而是预测股票的整个收益分布。通过估计公司超出或未达到分析师预期的可能性，帮助交易者做出更好的决策。使用非参数机器学习模型进行精确预测。

- **资产**：美国上市股票
- **交易逻辑**：做多预计超预期收益可能性高的公司，做空预计低的公司。
- **基本原理**：收益分布的预测提供了对未来股票表现的更好洞察，尤其是在收益意外的情况下。
- **平衡周期**：每季（围绕收益公告）

### 3. 使用机器学习预测期权回报

此策略利用机器学习预测期权回报，通过包括流动性、波动性等因素的大数据集识别错定价的期权，并基于预测回报创建多空组合。

- **资产**：美国股票期权
- **交易逻辑**：基于广泛的特征集（包括隐含波动性和流动性），做多预测回报强劲的期权，做空预测回报弱的期权。
- **基本原理**：通过分析大量的期权和股票特征数据，机器学习可以发现期权定价错误及套利机会。
- **平衡周期**：每月（根据市场情况动态调整）

### 4. 使用机器学习构建因子投资组合

该策略通过分析数百个股票特征，使用机器学习构建一个能够超越传统因子模型的投资组合。关键优势在于能够动态适应市场条件的变化，特别是在信用周期的不同阶段。

- **资产**：美国上市股票
- **交易逻辑**：基于机器学习模型识别影响回报的主导因子，做多预测的赢家，做空预测的输家。
- **基本原理**：股票回报的驱动因素会随着时间变化，机器学习有助于捕捉这些变化，产生比静态因子模型更好的投资组合表现。
- **平衡周期**：每月

### 5. 使用机器学习评估公司质量

此策略使用机器学习更准确地评估公司的质量，通过分析财务数据，帮助投资者和利益相关者做出更好的决策，发现可能在未来表现良好的优质公司。

- **资产**：美国上市股票
- **交易逻辑**：根据机器学习预测的公司质量评分，做多被低估的高质量公司。
- **基本原理**：机器学习通过分析细化的财务数据可以更准确地评估公司质量，从而更好地预测未来表现。
- **平衡周期**：每年

### 6. 使用机器学习预测日内回报

该策略旨在利用LSTM模型预测日内股票回报的方向。通过观察过去的日内价格，模型可以在交易日内进行有利可图的交易，但最近的回测表明仍需改进。

- **资产**：S&P 500 股票
- **交易逻辑**：使用LSTM预测，做多日内表现可能最好的前10只股票，做空表现可能最差的后10只股票。
- **基本原理**：过去的日内股票价格走势有助于预测未来的日内回报，提供有利可图的短期交易机会。
- **平衡周期**：日内

该方向适合对机器学习感兴趣的同学，尤其是希望通过编程实践深入学习的你。

## 衍生品方向

### 内容简介

如果你对期权及其他衍生品有兴趣，且希望将其应用于个人交易策略中，那么本模块非常适合你。我们将带你深入理解期权的定价模型和对冲策略。

### 课程内容

1. **理论学习**：
   - 期权基础：BS模型及其应用
   - 希腊字母的动态分析
   - 波动率曲面及随机波动率模型
   - 奇异期权的定价实现
2. **实践项目**：结合数值方法与衍生品策略，构建回测系统并进行优化测试。

该方向特别适合对衍生品交易和复杂金融工具感兴趣的同学，具备一定编程基础更佳。

---

## LLMQuant实践项目mini-Project

# 1. 期权定价项目 (Option Pricing)

### 项目介绍

该项目深入探讨了期权定价模型及其在对冲策略中的应用。项目包含编程和数学练习，旨在帮助剑桥LLMQuant和北大QTA同学理解期权定价的不同方法与实际应用。

### 主要内容

1. **布莱克-舒尔斯期权定价模型**  
   - 包括 Itô 微积分练习
2. **随机微分方程的离散化与蒙特卡洛期权定价**  
   - 使用数值方法模拟期权价格
3. **基于市场敏感度的动态对冲策略**  
   - 包括 delta、gamma、vega 的应用
4. **Dupire 模型和 Heston 模型**
   - 两个重要的波动率模型
5. **基于快速傅里叶变换的期权定价模型**  
   - Carr 和 Madan 模型
6. **复杂期权简介**
   - 例如障碍期权等
7. **算法交易策略中的实际应用示例**

# 2. 限价订单簿项目 (Limit Order Books)

### 项目介绍

该项目专注于限价订单簿及其在市场微观结构中的运作，主要探讨交易所如何匹配订单，以及各种算法和订单类型的实现。此项目以编程练习为主，较少数学内容。

### 主要内容

1. **限价订单簿的概念**
   - 涵盖限价单与市价单
   - 实现基于价格-时间算法的订单簿
   - 探讨订单取消与市场诱导策略（如市场诱骗）
2. **匹配算法**
   - 实现比例匹配算法，并扩展到包括顶级订单和流动性提供商（LMM）
   - 添加其他订单类型（市价单、止损单、冰山单等）
3. **当前资产公平价格**
   - 计算中间价和加权版本
   - 基于马尔科夫模型的微观价格
   - 使用二级市场数据的启发式方法
4. **扩展建议**
   - 使用深度学习预测未来订单簿状态并进行交易的扩展研究

# 3. 隐含波动率与波动率曲面项目 (Implied Volatility and Volatility Surfaces)

### 项目介绍

该项目集中讲解如何计算隐含波动率、绘制波动率曲面，并将其应用于交易策略中。项目包含大量编程和数学练习，剑桥LLMQuant-北大QTA同学将学习如何处理实际市场数据并分析波动率。

### 主要内容

1. **波动率曲面概念**
   - 介绍布莱克-舒尔斯定价模型
   - 隐含波动率的定义及存在性条件
2. **获取期权链数据**
   - 使用 yfinance 库获取期权链数据
   - 将期权链信息整理为 pandas 数据框架
3. **计算隐含波动率**
   - 特殊情况下隐含波动率的解析解
   - 使用二分法和牛顿-拉弗森法计算隐含波动率
   - 使用布伦特法进行扩展
4. **插值与绘制波动率曲面**
   - 确保波动率曲面无套利
   - 使用线性插值和三次样条插值绘制曲面
5. **波动率曲面的特性**
   - 波动率的偏斜、微笑与期限结构的讨论
   - 各种市场中波动率曲面的典型形态
6. **实际应用的扩展**
   - 外汇市场中的跨式期权交易
   - 通过波动率期限结构预测期权回报

---

## 报名方式

本次内培针对剑桥LLMQuant社区和北京大学量化交易协会成员开放，无需具备丰富的量化经验。LLMQuant社区目前已**对外开放**，参加未来的内部课程与内部讲座，可扫描以下二维码申请成为LLMQuant成员，附上简历可以更快获得申请结果～

立即扫码报名吧！

---

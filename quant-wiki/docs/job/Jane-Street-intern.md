![](https://fastly.jsdelivr.net/gh/bucketio/img11@main/2024/10/21/1729466068183-23134fce-3131-4262-b18c-f378d71af4f6.gif)

# 从顶尖大学到顶尖量化公司：探秘Jane Street实习生的亲身经历

![](https://fastly.jsdelivr.net/gh/bucketio/img9@main/2024/10/20/1729465031968-b3c8959e-1d37-4b8a-91b1-b0b0dfe25143.png)

本文将通过三位曾在Jane Street实习并成功转为全职员工的同学的亲身经历，带您深入了解其技术实习项目。

Jane Street是一家全球知名的量化交易公司和流动性提供商，成立于2000年，总部位于美国纽约市。公司在全球拥有超过2,600名员工，业务遍及纽约、伦敦、香港、阿姆斯特丹、芝加哥和新加坡等地。

作为主要的做市商之一，Jane Street在全球超过200个交易场所进行交易，涵盖股票、债券、期权、期货、商品、数字资产和货币等多种资产类别。 2020年，公司交易的证券总价值超过17万亿美元。

Jane Street以其技术驱动的交易策略而著称，广泛使用OCaml编程语言开发交易系统和工具。 公司强调协作和问题解决，致力于在技术和交易领域的创新。

![](https://fastly.jsdelivr.net/gh/bucketio/img12@main/2024/11/26/1732636375053-896444d7-81a0-43ad-b0cb-e89be0aa622a.png)

## 函数式编程之路：从校园到实习

### 同学A的故事

**背景**：同学A就读于卡内基梅隆大学（CMU），这所大学以其强大的编程语言研究团队和丰富的函数式编程课程而闻名。在校期间，同学A对函数式编程产生了浓厚的兴趣，不仅积极学习，还亲自教授了一门名为“Hype for Types”的课程，旨在推广函数式编程的理念。

**如何了解到公司**：在课堂上，经常有同学会问：“函数式编程什么时候会在实际中有用？”大家常常会提到这家公司，但又笑称“别说这家公司，因为他们是唯一一家大规模使用函数式编程的公司。”这引起了同学A的兴趣。

**申请实习的动机**：出于对函数式编程的热爱，同学A觉得申请这家公司的实习项目是顺理成章的选择。她希望能在实际项目中运用所学，并与志同道合的人一起工作。

### 同学B的故事

**背景**：同学B就读于哈佛大学，主修计算机科学和统计学。在学校期间，她参加了一门函数式编程课程，对OCaml产生了兴趣。课程期间，Ron Minsky（一位知名技术专家）前来演讲，讨论了OCaml在实际中的应用，这进一步激发了她的兴趣。

**如何了解到公司**：同学B参与了公司的INSIGHT项目，这是一个专为STEM女性举办的为期一周的训练营。该项目在公司的纽约办公室举行，参与者通过一系列OCaml练习和项目，深入了解公司的技术文化和工作环境。

**申请实习的动机**：在INSIGHT项目中，同学B感受到了公司的开放文化和技术实力。她与公司员工的互动让她决定申请实习，希望能进一步深入参与实际项目。

### 同学C的故事

**背景**：同学C就读于剑桥大学，在校期间接触了函数式编程。虽然OCaml在他的学校并不像在其他地方那样普及，但这并未阻碍他的兴趣。

**如何了解到公司**：同学C的一位朋友曾在这家公司实习过，向他分享了在那里的有趣经历和收获。这激发了同学C对该公司的浓厚兴趣。

**申请实习的动机**：受到朋友的推荐和对函数式编程的热爱，同学C决定申请这家公司的实习，希望能在实际环境中运用所学，并接受新的挑战。

![](https://fastly.jsdelivr.net/gh/bucketio/img19@main/2024/11/26/1732636409842-603d73f7-fe42-4910-aaff-5dd846a4d84a.png)

## 实习之旅：深入参与核心项目

### 同学A的实习经历

**项目概述**：同学A参与了公司的手动订单输入系统的改进。该系统允许交易员手动向市场下单，并具有多种风险控制措施。

**挑战与解决方案**：

- **问题**：交易员无法在交易日内快速调整系统级别的风险限制，这在市场波动时可能影响交易效率。之前的流程需要更改配置文件，经过代码审查和发布，速度较慢。
- **同学A的任务**：优化系统，使交易员能够在交易日内迅速调整系统级别的风险限制，同时确保合规性和安全性。
- **实施过程**：她深入了解现有的风险控制机制，与团队密切合作，设计并实现了一个允许快速调整风险限制的模块。她还确保了新功能与现有系统的兼容性。
- **成果**：同学A成功实现了这一功能，提升了系统的灵活性。她的工作在全职加入后得到了广泛应用，交易员们对新功能赞不绝口。

### 同学B的实习经历

**项目概述**：同学B致力于Incr_dom库的开发，这是公司用于构建动态网页应用的OCaml库。

**挑战与解决方案**：

- **问题**：现有的Incr_dom库有两个接口，一个简单但功能受限，另一个功能强大但复杂度高，使用者需要在易用性和性能之间做出选择。
- **同学B的任务**：设计并实现一个新的接口，结合两者的优点，既保持易用性，又不失功能性。
- **实施过程**：她深入研究了库的架构，思考了API设计原则。她还编写了一个PPX（预处理扩展），简化了开发者的使用方式。过程中，她与导师密切合作，反复迭代设计方案。
- **成果**：同学B成功地设计了新的接口，并改进了库的性能。她的工作提高了开发效率，受到了团队的认可。

### 同学C的实习经历

**项目概述**：同学C参与了请求报价（RFQ）系统的开发。RFQ系统允许客户向多家做市商请求报价，然后选择最佳价格进行交易。

**挑战与解决方案**：

- **问题**：手动请求的风险检查流程不够自动化，存在效率和安全隐患。手动请求没有应用与自动请求相同的风险控制机制。
- **同学C的任务**：将手动请求与自动化系统整合，使其能够使用统一的风险检查机制，确保交易安全。
- **实施过程**：他需要深入理解RFQ系统的架构和交易流程，与团队和交易员沟通需求。他重新设计了手动请求的处理流程，使其能够被系统识别并应用风险检查。
- **成果**：同学C成功地使手动请求的风险检查流程自动化，提高了系统的安全性和效率，减少了人为错误的可能性。

![](https://fastly.jsdelivr.net/gh/bucketio/img10@main/2024/11/26/1732636504579-6a7747a9-7dc1-4f96-b0b6-8253287a0e3d.png)

## 从实习到全职：新的挑战与成长

### 同学A的转正之路

- **回归团队**：同学A选择回到她实习期间的团队，继续深化她的项目。
- **新的责任**：作为全职员工，她参与了更多长期和开放性的项目，如期权定价应用的开发。她需要考虑更广泛的系统设计和性能优化问题。
- **合作与创新**：她积极与不同团队合作，引入新的技术解决方案。例如，在项目中采用Redis进行缓存，提高了系统性能和响应速度。

### 同学B的转正之路

- **团队选择**：同学B加入了同学C曾实习的团队，承担了推进RFQ系统项目的任务。
- **深入合作**：她需要与更多的交易员和团队紧密合作，了解他们在交易过程中遇到的问题，收集改进需求。
- **技术提升**：在新的团队中，同学B不断提升自己的技术能力，参与了复杂的系统设计、风险控制和用户界面优化。

### 同学C的转正之路

- **新项目挑战**：同学C加入了一个新的团队，开始了Zeroprot项目的开发。Zeroprot是一个用于在不同应用之间高效通信的协议，旨在解决应用间版本兼容和高性能通信的问题。
- **关键角色**：同学C在项目中负责新功能的开发、协议的设计，以及与其他系统的集成。他还需要编写工具和库，方便其他开发者使用该协议。
- **技术突破**：他在项目中解决了诸多技术难题，如数据序列化、版本兼容性检查和高效的网络通信，为公司通信协议的优化做出了重要贡献。

## 跨团队协作：沟通与共赢

- **同学A的体验**：她强调了公司开放的沟通文化。在项目中，她与基础设施团队、风险控制团队密切合作，共同实现了系统性能的提升。她还通过与其他团队的交流，学习了新的技术和最佳实践。
- **同学B的体验**：每天与交易员沟通，使她深刻体会到了解用户需求的重要性。她通过直接的互动，快速响应业务需求，提供了高效的技术支持。她还参与了用户界面的改进，使系统更易用。
- **同学C的体验**：在大型项目中，同学C与多个团队协作，协调不同的技术方案和需求。他认为，成功的项目离不开良好的团队合作和沟通。他还积极参与了公司内部的技术分享会，分享自己的经验和心得。

## 面对挑战：在实践中成长

- **技术挑战**：适应全新的工具和技术，如OCaml、内部开发工具，以及复杂的系统架构，是他们共同的挑战。他们通过学习和实践，逐步掌握了必要的技能。
- **金融知识**：深入理解复杂的金融概念、交易流程和风险控制机制，对于没有相关背景的他们来说也是一大挑战。他们通过公司的培训课程、与同事的交流，以及自学，补足了这方面的知识。
- **自信与突破**：在高水平的团队中，他们需要不断建立自信，克服自我怀疑。他们意识到，学习和成长是一个持续的过程，积极寻求帮助和反馈。
- **支持与资源**：公司提供了丰富的资源和支持性环境，如导师制度、培训课程和开放的沟通渠道，使他们能够在挑战中不断成长。
- **文化氛围**：开放的企业文化和对新想法的包容，使他们能够自由地表达观点，积极参与决策。他们的建议和想法得到了认可和采纳，增强了他们的归属感和责任感。

---

## 关于LLMQuant

LLMQuant是由一群来自世界顶尖高校和量化金融从业人员组成的前沿社区，致力于探索人工智能（AI）与量化（Quant）领域的无限可能。我们的团队成员来自剑桥大学、牛津大学、哈佛大学、苏黎世联邦理工学院、北京大学、中科大等世界知名高校，外部顾问来自Microsoft、HSBC、Citadel、Man Group、Citi、Jump Trading、国内顶尖私募等一流企业。

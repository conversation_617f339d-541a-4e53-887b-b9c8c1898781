👍🎉 首先，感谢您抽出宝贵时间为 **Quant Wiki** 做出贡献！ 👍🎉

> [!TIP]
>
> 这里给出基本修改/更新页面内容的流程（符合 80% 的场景）
>
> - fork 这个 repo。
> - 在 `docs` 文件夹中增添或修改对应的文件/文件夹。
> - （如果你新增了文件或者文件夹）请在 `mkdocs.yml` 中的 `nav` 字段下进行更新。
> - 新建 issue 说明当前 repo 存在的问题
> - 提交对应的 Pull Request 即可。

请您在提交拉取请求（Pull Request）前再次阅读[如何参与的有关内容](https://quant-wiki.com/contribute/)，尤其应再次确认**对于目录和引用的变更**和**信息格式规范**。

如果有关内容**尚未完成**，请考虑先[新建 issue](https://github.com/LLMQuant/quant-wiki/issues/new/choose) 或本次作为 draft pr 提交。

如果内容**已经完成**，您无需新建 issue。

在提交 pull request 的[Checklist](./PULL_REQUEST_TEMPLATE.md)中，勾选你已经阅读的条目，表明**您已知晓**：

+ 请在回应建议、问题或 pull request 之前仔细阅读，并详细说明自己的看法，以免引起不必要的误会。

+ 请跟进您的 pull request。如您的 pull request 长时间没有回应修改请求，可能会被直接关闭。

+ 我们欢迎您审核其他 pull request，但请以友好的方式发表评论。负面评论会打击社区贡献者的贡献热情，因此不建议这样做。当你在 pull request 中发现问题时，欢迎进一步的 pull request，但不欢迎你对贡献者（pull request 作者）发表负面评价。

+ 请记住，您在一个社区中，您需要学会接受其他人的贡献，以及他们贡献的内容，甚至可能和他们协作。如果您不同意这个观点，您可以创建分支并自己进行更改。另一方面，你的想法可能不够完善，所以要听取别人的意见。

最后，当您为社区做出贡献时，您亦同意 Quant Wiki 组织可以禁止违反社区准则并破坏社区和谐的用户访问组织。如果您发现有人不遵守此处的指南，欢迎联系 [<EMAIL>](mailto:<EMAIL>)。感谢您为本项目及其社区和谐发展提供的支持和力量！:joy:

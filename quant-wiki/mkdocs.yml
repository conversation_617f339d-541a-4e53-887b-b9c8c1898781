site_name: Quant Wiki 中文量化百科
site_url: https://quant-wiki.com
site_author: LLMQuant Team
site_description: A comprehensive quantitative trading and finance wiki

# Repository
repo_name: LLMQuant/quant-wiki
repo_url: https://github.com/LLMQuant/quant-wiki
edit_uri: edit/main/docs/

# Copyright
copyright: Copyright &copy; 2025 LLMQuant Team


# Navigation
nav:
  - 简介: 
    - 关于项目: index.md
    - 如何参与: contribute.md
    - 常见问题: FAQ.md
    - 关于LLMQuant: about.md
    - 社区其他项目: other.md
    - 加入我们: join.md
  - 基本概念:
    - 知识框架: basic/index.md
    - 金融术语: 
      - 概述: basic/finance/index.md
      - 市场与交易:
        - 一级市场: basic/finance/一级市场_Primary Market.md
        - 二级市场: basic/finance/二级市场_Secondary Market.md
        - 债券市场: basic/finance/债券市场_Bond Market.md
        - 外汇市场: basic/finance/外汇市场_Foreign Exchange.md
        - 外汇: basic/finance/外汇_Forex.md
        - 股市: basic/finance/股市_Stock Market.md
        - 熊市: basic/finance/熊市_Bear Market.md
        - 牛市: basic/finance/牛市_Bull Market.md
        - 纳斯达克: basic/finance/纳斯达克_Nasdaq.md
      - 金融工具:
        - 股权: basic/finance/股权_Equity.md
        - 期货: basic/finance/期货_Futures.md
        - 期权: 
          - 二元期权: basic/finance/二元期权_Binary Option.md
          - 卖出期权: basic/finance/卖出期权_Put Option.md
          - VIX期权: basic/finance/VIX期权_VIX Option.md
        - 债券:
          - 债券: basic/finance/债券_Bond.md
          - 国债: basic/finance/国债_Treasury Bond.md
          - 国库券: basic/finance/国库券_Treasury Notes.md
          - 可转换债券: basic/finance/可转换债券_Convertible Bond.md
        - 证券: basic/finance/证券_Security.md
        - 衍生品: basic/finance/衍生品_Derivative.md
        - 标的资产: basic/finance/标的资产_Underlying Asset.md
        - 固定收益: basic/finance/固定收益_Fixed Income.md
        - 远期价格: basic/finance/远期价格_Forward Price.md
        - 远期合同: basic/finance/远期合同_Forward Contract.md
      - 交易机制:
        - T+1: basic/finance/T+1_T+1.md
        - 保证金: basic/finance/保证金_Margin.md
        - 保证金交易: basic/finance/保证金交易_Buying on Margin.md
        - 交易商: basic/finance/交易商_Dealer.md
        - 卖空: basic/finance/卖空_Short Selling.md
        - 首次公开募股: basic/finance/首次公开募股_Initial Public Offerings (IPOs).md
        - 报价: basic/finance/报价_Quotation.md
        - 首席经纪业务: basic/finance/首席经纪业务_Prime Brokerage.md
      - 投资理论:
        - 价值投资: basic/finance/价值投资_Value Investing.md
        - 被动投资: basic/finance/被动投资_Passive Investing.md
        - 多因子模型: basic/finance/多因子模型_Multi-Factor Model.md
        - 有效市场假说: basic/finance/有效市场假说_Efficient Market Hypothesis.md
        - 风险投资: basic/finance/风险投资_Venture Capital.md
        - 对冲: basic/finance/对冲_Hedge.md
      - 经济指标与概念:
        - 国内生产总值: basic/finance/国内生产总值_Gross Domestic Product (GDP).md
        - 国民生产总值: basic/finance/国民生产总值_Gross National Product (GNP).md
        - 生产者价格指数: basic/finance/生产者价格指数_Producer Price Index (PPI).md
        - 通货膨胀: basic/finance/通货膨胀_Inflation.md
        - 恶性通胀: basic/finance/恶性通胀_Hyperinflation.md
        - 失业: basic/finance/失业_Unemployment.md
        - 经济增长: basic/finance/经济增长_Economic Growth.md
        - 商业周期: basic/finance/商业周期_Business Cycle.md
        - 大萧条: basic/finance/大萧条_Great Depression.md
        - 房地产泡沫: basic/finance/房地产泡沫_Housing Bubble.md
        - 利率: basic/finance/利率_Interest Rate.md
        - 联邦基金利率: basic/finance/联邦基金利率_Federal Funds Rate.md
      - 经济理论与政策:
        - 宏观经济学: basic/finance/宏观经济学_Macroeconomics.md
        - 凯恩斯经济学: basic/finance/凯恩斯经济学_Keynesian Economics.md
        - 新自由主义: basic/finance/新自由主义_Neoliberalism.md
        - 资本主义: basic/finance/资本主义_Capitalism.md
        - 自由市场: basic/finance/自由市场_Free Market.md
        - 自由贸易: basic/finance/自由贸易_Free Trade.md
        - 公开市场操作: basic/finance/公开市场操作_Open Market Operations.md
        - 货币政策: basic/finance/货币政策_Monetary Policy.md
        - 关税: basic/finance/关税_Tariff.md
        - 贸易逆差: basic/finance/贸易逆差_Trade Deficit.md
        - 量化宽松: basic/finance/量化宽松_Quantitative Easing.md
      - 公司与市场结构:
        - 股份公司: basic/finance/股份公司_Joint-Stock Company.md
        - 有限合伙: basic/finance/有限合伙_Limited Partnership (LP).md
        - 寡头垄断: basic/finance/寡头垄断_Oligopoly.md
        - 规模经济: basic/finance/规模经济_Economies of Scale.md
        - 知识经济: basic/finance/知识经济_Knowledge Economy.md
        - 债务重组: basic/finance/债务重组_Debt Restructuring.md
        - 合并与收购: basic/finance/合并与收购_Mergers and Acquisitions.md
        - 杠杆收购: basic/finance/杠杆收购_Leveraged Buyout.md
        - 经济订货量: basic/finance/经济订货量_Economic Order Quantity.md
        - 长期资本管理公司: basic/quant/长期资本管理公司_Long-Term Capital Management.md
      - 财务指标与概念:
        - 市盈率: basic/finance/市盈率_Price-to-Earnings Ratio (PdivE Ratio).md
        - 股息: basic/finance/股息_Dividend.md
        - 股权稀释: basic/finance/股权稀释_Dilution.md
        - 毛利率: basic/finance/毛利率_Gross Profit Margin.md
        - 贴现率: basic/finance/贴现率_Discount Rate.md
        - 年金表: basic/finance/年金表_Annuity Table.md
        - 收益率倒挂: basic/finance/收益率倒挂_Inverted Yield Curve.md
        - AAA信用评级: basic/finance/AAA信用评级_AAA信用评级_AAA.md
        - CAPE比率: basic/finance/CAPE比率_CAPE Ratio.md
        - 中型市值: basic/finance/中型市值_Mid-Cap.md
        - 变化率: basic/finance/变化率_Rate of Change.md
        - 基准年: basic/finance/基准年_What Is a Base Year.md
        - 增长曲线: basic/finance/增长曲线_Growth Curve.md
        - 增长率: basic/finance/增长率_Growth Rates.md
        - 复利: basic/finance/复利_Compounding.md
        - 复合年增长率: basic/finance/复合年增长率_Compound Annual Growth Rate.md
        - 年回报率: basic/finance/年回报率_Annual Return.md
        - 年金未来价值: basic/finance/年金未来价值_Future Value of an Annuity.md
        - 现值: basic/finance/现值_Present Value.md
        - 资产负债表: basic/finance/资产负债表_Balance Sheet.md
        - 资本化: basic/finance/资本化_Capitalization.md
        - 边际收益: basic/finance/边际收益_Marginal Benefits.md
        - 面值: basic/finance/面值_Face Value.md
      - 风险与波动:
        - 杠杆: basic/finance/杠杆_Leverage.md
        - 杠杆率: basic/finance/杠杆率_Leverage Ratio.md
        - 无杠杆Beta: basic/finance/无杠杆Beta_Unlevered Beta.md
        - 波动性: basic/finance/波动性_Volatility.md
        - 流动性: basic/finance/流动性_Liquidity.md
        - CBOE波动率指数: basic/finance/CBOE波动率指数_VIX.md
      - 其他概念:
        - 二八法则: basic/finance/二八法则_80-20 Rule.md
        - 基尼系数: basic/finance/基尼系数_Gini Index.md
        - 菲利普斯曲线: basic/finance/菲利普斯曲线_Phillips Curve.md
        - 比较优势: basic/finance/比较优势_Comparative Advantage.md
        - 绝对优势: basic/finance/绝对优势_Absolute Advantage.md
        - 汇率: basic/finance/汇率_Exchange Rate.md
        - 货币流通速度: basic/finance/货币流通速度_Velocity of Money.md
    - 概率基础:
      - 概述: basic/prob/index.md
      - 基础理论:
        - 条件概率: basic/prob/条件概率_Conditional Probability.md
        - 联合概率: basic/prob/联合概率_Joint Probability.md
        - 贝叶斯定理: basic/prob/贝叶斯定理_Baye's Theorem.md
        - 相关性: basic/prob/相关性_Correlation.md
        - 相关系数: basic/prob/相关系数_Correlation Coefficient.md
      - 概率分布:
        - 概率分布: basic/prob/概率分布_Probability Distribution.md
        - 正态分布: basic/prob/正态分布_Normal Distribution.md
        - 均匀分布: basic/stat/均匀分布_Uniform Distribution.md
      - 重要定理:
        - 大数法则: basic/prob/大数法则_Law of Large Numbers.md
        - 中心极限定理: basic/stat/中心极限定理_Central Limit Theorem.md
        - 经验法则: basic/stat/经验法则_Empirical Rule.md
      - 应用:
        - 蒙特卡罗模拟: basic/prob/蒙特卡罗模拟_Monte Carlo Simulation.md
        - 系统抽样: basic/prob/系统抽样_Systematic Sampling.md
        - 变异系数: basic/prob/变异系数_Coefficient of Variation (CV).md
      - 金融衍生品:
        - 衍生品: basic/prob/衍生品_Derivative.md
    - 统计基础:
      - 概述: basic/stat/index.md
      - 基本概念:
        - 期望值: basic/stat/期望值_Expected Value.md
        - 协方差: basic/stat/协方差_Covariance.md
        - 相关系数: basic/stat/相关系数_Correlation Coefficient.md
        - 线性关系: basic/stat/线性关系_Linear Relationship.md
        - 非线性: basic/stat/非线性_Nonlinearity.md
        - 自相关: basic/stat/自相关_Autocorrelation.md
        - 多重共线性: basic/stat/多重共线性_Multicollinearity.md
      - 统计检验:
        - P值: basic/prob/P值_P-Value.md
        - Z值: basic/prob/Z值_Z-Score.md
        - Z检验: basic/prob/Z检验_Z-Test.md
        - T检验: basic/prob/T检验_T-Test.md
        - 假设检验: basic/stat/假设检验_Hypothesis Testing.md
        - 统计显著性: basic/stat/统计显著性_Statistical Significance.md
        - 卡方统计量: basic/stat/卡方统计量_Chi Square Statistic.md
        - 置信区间: basic/stat/置信区间_Confidence Interval.md
      - 回归分析:
        - 回归分析: basic/stat/回归分析_Regression.md
        - R平方: basic/stat/R平方_R-Squared.md
        - 决定系数: basic/stat/决定系数_Coefficient of Determination.md
        - 多元线性回归: basic/stat/多元线性回归_Multiple Linear Regression.md
        - 最小二乘法: basic/stat/最小二乘法_Least Squares Method.md
        - 变量膨胀因子: basic/stat/变量膨胀因子_Variance Inflation Factor.md
      - 方差分析:
        - 方差分析: basic/stat/方差分析_Analysis of Variance.md
      - 经典模型:
        - 默顿模型: basic/stat/默顿模型_Merton Model.md
    - 量化术语:
      - 概述: basic/quant/index.md
      - 交易策略:
        - 趋势交易: basic/quant/趋势交易_Trend Trading.md
        - 动量投资: basic/quant/动量投资_Momentum Investing.md
        - 因子投资: basic/quant/因子投资_Factor Investing.md
        - 高频交易: basic/quant/高频交易_High-Frequency Trading.md
        - 均值回归: basic/quant/均值回归_Mean Reversion.md
        - 套利者: basic/quant/套利者_Arbitrageur.md
        - 市场中性: basic/quant/市场中性_Market Neutral.md
        - 货币套利交易: basic/quant/货币套利交易_Currency Carry Trade.md
        - 新闻交易者: basic/quant/新闻交易者_News Trader.md
      - 期权策略:
        - 德尔塔对冲: basic/quant/德尔塔对冲_Delta Hedging.md
        - 伽马对冲: basic/quant/伽马对冲_Gamma Hedging.md
        - 波动率套利: basic/quant/波动率套利_Volatility Arbitrage.md
        - 德尔塔中性: basic/quant/德尔塔中性_Delta Neutral.md
        - 伽玛中性: basic/quant/伽玛中性_Gamma Neutral.md
        - 看跌期权: basic/quant/看跌期权_Long Put.md
        - 跨式期权: basic/quant/跨式期权_Straddle.md
        - 空头看涨价差: basic/quant/空头看涨价差_Bear Call Spread.md
        - 波动率微笑: basic/quant/波动率微笑_Volatility Smile.md
      - 技术指标:
        - 移动平均线: basic/quant/移动平均线_Moving Average.md
        - 简单移动平均线: basic/quant/简单移动平均线_Simple Moving Average.md
        - 指数移动平均线: basic/quant/指数移动平均线_Exponential Moving Average.md
        - 相对强弱指数: basic/quant/相对强弱指数_Relative Strength Index.md
        - 费舍尔变换指标: basic/quant/费舍尔变换指标_Fisher Transform Indicator.md
        - 双顶: basic/quant/双顶_Double Top.md
        - 黄金交叉: basic/quant/黄金交叉_Golden Cross.md
      - 基金类型:
        - 多空基金: basic/quant/多空基金_LongdivShort Fund.md
        - 多空股权: basic/quant/多空股权_LongdivShort Equity.md
      - 交易订单:
        - 市价单: basic/quant/市价单_Market Order.md
        - 限价单: basic/quant/限价单_Limit Order.md
        - 限价单簿: basic/quant/限价单簿_Limit Order Book.md
        - 立即执行或取消订单: basic/quant/立即执行或取消订单_Immediate Or Cancel Order.md
        - 限时订单: basic/quant/限时订单_Held Order.md
      - 头寸管理:
        - 空头头寸: basic/quant/空头头寸_Short.md
        - 逼空: basic/quant/逼空_Short Squeeze.md
      - 希腊字母指标:
        - 伽马: basic/quant/伽马_Gamma.md
        - 贝塔: basic/quant/贝塔_Beta.md
        - 阿尔法: basic/quant/阿尔法_Alpha.md
      - 经典模型:
        - 资本资产定价模型: basic/quant/资本资产定价模型_Capital Asset Pricing Model.md
        - Fama-French三因子模型: basic/quant/Fama-French三因子模型_Fama and French Three Factor Model.md
      - 分析工具:
        - 回测: basic/quant/回测_Backtesting.md
      - 历史人物:
        - 本杰明·格雷厄姆: basic/quant/本杰明·格雷厄姆_Who Was Benjamin Graham.md
        - 杰西·L·利弗莫尔: basic/quant/杰西·L·利弗莫尔_Jesse L. Livermore.md
  - 入门教程: 
    - 量化交易员带你入门:
      - 为什么有些交易策略能带来盈利？: start/quant_trader/为什么有些交易策略能带来盈利.md
      - 如何打造"好用"的交易策略: start/quant_trader/如何打造"好用"的交易策略.md
      - 如何如何划分交易风格？: start/quant_trader/如何如何划分交易风格.md
      - 量化交易员带你写Long-Short Strategy代码: start/quant_trader/long-short.md
      - 量化术语簿: start/translated_Quant_Trading_101.md
      - 量化交易竞赛: start/translated_Quant_Trading_Competitions.md
      - 为有志于量化领域的人士展示他们技能给雇主的绝佳项目集: start/translated_Quantitative_Finance_Portfolio_Projects.md
    - 必懂概念入门: 
      - 夏普比率: start/sharpe.md
      - 期权定价: start/option.md
      - 波动率: start/volatility.md
      - 资产组合理论: start/mpt-markowitz.md
      - 高频交易: start/high-frequency.md
      - 极值理论(EVT)在VaR与ES计算中的应用: start/evt-var-es.md
    - 策略类型入门:
      - 一文解密量化策略类型: start/strategy-type.md
      - 多策略对冲基金入门: start/multi-strategy.md
      - 事件驱动型: start/event-driven.md
      - 宏观对冲基金入门: start/macro-hedge.md
    - 实用行业入门:
      - 机构策略九个热门策略: start/nine-strategies.md
      - Point72投资策略: start/point72-idea.md
  - 学术论文:
    - 论文清单: scholar/index.md
  - 量化前沿:
    - 简介: advanced/index.md
    - 量化最新研究:
      - 最新研究目录: advanced/最新技术/index.md
      - 业内使用案例:
        - 对冲基金巨头布局AI量化: advanced/最新技术/ChatGPT-Balyasny.md
      - 前沿技术:
        - 使用大语言模型揭露企业年报中掩盖的坏消息: advanced/最新技术/llm-report.md
        - RD-Agent 革新金融量化交易的AI自动化工具: advanced/最新技术/rd-agent.md
        - 量化投资专家的投资组合管理指南: advanced/最新技术/Advanced Portfolio Management.md
        - 量化中的常见统计学与机器学习概念(新版): advanced/最新技术/translated_Statistics_&_ML_Concepts_for_your_Next_Quant_Finance_Interview_new.md
        - 前沿论文: advanced/最新技术/translated_Important_Research_Papers_for_Quants.md
    - 研报精选:
      - 研报精选目录: advanced/研报精选/index.md
      - 多因子系列:
        - 中信多因子: advanced/研报精选/中信-多因子系列/index.md
        - 华泰多因子: advanced/研报精选/华泰-多因子系列/index.md
        - 国盛多因子: advanced/研报精选/国盛-多因子系列/index.md
        - 广发多因子: advanced/研报精选/广发-多因子系列/index.md
        - 方正财通星火: advanced/研报精选/方正财通-星火系列/index.md
        - 海通选股因子: advanced/研报精选/海通-选股因子系列/index.md
        - 渤海多因子: advanced/研报精选/渤海-多因子系列/index.md
      - 人工智能系列:
        - 华泰人工智能: advanced/研报精选/华泰-人工智能系列/index.md
        - 广发深度学习: advanced/研报精选/广发-深度学习系列/index.md
      - 高频交易系列:
        - A股高频研报: advanced/研报精选/a股高频研报/index.md
        - 广发高频因子: advanced/研报精选/广发-高频因子系列/index.md
      - 其他系列:
        - 财通中信逐鹿: advanced/研报精选/财通中信-逐鹿系列/index.md
  - 量化百宝箱:  
    - 简介: repo/quant_learn.md
    - 量化学习资源: 
      - 开源工具库: 
        - 回测与实盘交易: repo/quant_learn/#backtesting-and-live-trading
        - 事件驱动框架: repo/quant_learn/#general---event-driven-frameworks
        - 向量化框架: repo/quant_learn/#general---vector-based-frameworks
        - 加密货币框架: repo/quant_learn/#cryptocurrencies
        - 交易机器人: repo/quant_learn/#trading-bots
        - 量化框架与其Github仓库: repo/quant_repo
      - 分析工具:
        - 技术指标: repo/quant_learn/#indicators
        - 指标计算: repo/quant_learn/#metrics-computation
        - 优化工具: repo/quant_learn/#optimization
        - 定价工具: repo/quant_learn/#pricing
        - 风险分析: repo/quant_learn/#risk
        - 券商接口: repo/quant_learn/#broker-apis
      - 数据工具:
        - 数据源: repo/quant_learn/#data-sources
        - 通用数据: repo/quant_learn/#general
        - 加密货币数据: repo/quant_learn/#cryptocurrencies-1
        - 数据科学: repo/quant_learn/#data-science
        - 数据库: repo/quant_learn/#databases
        - 图计算: repo/quant_learn/#graph-computation
      - 高级分析:
        - 机器学习: repo/quant_learn/#machine-learning
        - 时间序列: repo/quant_learn/#timeseries-analysis
        - 数据可视化: repo/quant_learn/#visualization
      - 交易策略:
        - 多资产策略: repo/quant_learn/#bonds-commodities-currencies-equities
        - 债券商品股票REIT: repo/quant_learn/#bonds-commodities-equities-reits
        - 债券与股票: repo/quant_learn/#bonds-equities
        - 债券股票REIT: repo/quant_learn/#bonds-equities-reits
        - 商品策略: repo/quant_learn/#commodities
        - 加密货币策略: repo/quant_learn/#cryptos
        - 外汇策略: repo/quant_learn/#currencies
        - 股票策略: repo/quant_learn/#equities
      - 学习资源:
        - 视频资源: repo/quant_learn/#videos
        - 博客资源: repo/quant_learn/#blogs
        - 课程资源: repo/quant_learn/#courses
      - 不同编程语言的量化框架: 
        - Python: repo/quant_repo/#python
        - R: repo/quant_repo/#r
        - Matlab: repo/quant_repo/#matlab
        - Julia: repo/quant_repo/#julia
        - Java: repo/quant_repo/#java
        - JavaScript: repo/quant_repo/#javascript
        - Haskell: repo/quant_repo/#haskell
        - Scala: repo/quant_repo/#scala
        - Ruby: repo/quant_repo/#ruby
        - Elixir/Erlang: repo/quant_repo/#elixirerlang
        - Golang: repo/quant_repo/#golang
        - C++: repo/quant_repo/#cpp
        - C#: repo/quant_repo/#csharp
        - Rust: repo/quant_repo/#rust
      - 框架: repo/quant_repo/#frameworks
    - 研究成果复现: repo/reproduce.md
    - 趋势型: repo/trend-following.md
    - 统计套利型: repo/stat-arb.md
  - AI+量化: 
    - 简介: ai/index.md
    - 量化与人工智能结合: 
      - TradingAgents 多智能体LLM金融交易框架: ai/aiquant/TradingAgents.md
      - InvestorBench 面向LLM金融决策任务的Benchmark: ai/aiquant/InvestorBench.md
      - FinRobot 基于大语言模型的股票研究与估值框架: ai/aiquant/finrobot.md
      - ChatGPT也能做投资分析-手把手教你利用 LangChain搭建股票研究框架: ai/aiquant/ChatGPT-langchain.md
      - DeepSeek 一家用实力"做空"美国科技股的量化背景初创: ai/aiquant/deepseek.md
      - 如何利用LLM自动获取量化投资策略: ai/aiquant/如何利用LLM自动获取量化投资策略.md
      - TradeMaster强化学习: ai/aiquant/TradeMaster强化学习.md
      - GPT如何影响量化金融: ai/How_OpenAI_s_GPT_will_affect_Quantitative_Finance.md
    - 全面科普：谷歌 Gemini Flash 2.0 与 DeepSeek R1、OpenAI o3-mini 的对比与应用: ai/llm-overview.md
    - OpenAI发布号称"最强大"的GPT-4.5模型: ai/OpenAI发布号称"最强大"的GPT-4.5模型.md
    - 深度解析:如何用DeepSeek-R1对特斯拉相关新闻进行情感分析并生成投资建议: ai/DeepSeek-R1-tesla.md
    - 如何使用DeepSeek-R1或ChatGPT与Langchain构建专业金融分析师: ai/如何使用DeepSeek-R1或ChatGPT与Langchain构建专业金融分析师.md
    - 2025年AI量化论文优选41篇: ai/2025-ai-paper.md
    - 2024年AI量化论文精选: ai/2024-ai-paper.md
    - 2024年LLM量化论文: ai/2024-llm-quant-paper.md
    - AI量化交易基础: ai/ai-quant.md
    - ChatGPT量化实战: ai/ChatGPT-quant.md
    - ChatGPT选股策略: ai/ChatGPT-o1.md
    - 论文速读与复现: ai/chat-paper.md
    - 人工智能前沿:
      - Transformer架构详解: ai/llm/Transformer.md
      - DiffusionModel概述: ai/llm/Diffusion Models.md
      - VQVAE模型概述: ai/llm/VQVAE.md
  - 量化图书馆:
    - Overview: library/overview.md
    - 图书分类指南:
      - 入门级书籍: library/book/beginner.md
      - 进阶级书籍: library/book/advanced.md
      - 编程实现类: library/book/programming.md
      - AI量化类: library/book/ai-quant.md
    - 书籍:
      - 人工智能:
        - AI与机器学习:
          - AI for Finance: library/book/AI for Finance/index.md
          - Artificial Intelligence in Finance: library/book/Artificial Intelligence in Finance_ A Python-Based Guide/index.md
          - AI学习与经济计算: library/book/Artificial Intelligence, Learning and Computation in Economics and Finance/index.md
          - Deep Learning for Finance: library/book/Deep Learning for Finance/index.md
          - Machine Learning for Finance: library/book/Machine Learning for Finance/index.md
          - Machine Learning in Finance: library/book/Machine Learning in Finance_ From Theory to Practice/index.md
          - Financial Machine Learning: library/book/Financial Machine Learning/index.md
          - 金融信号处理与机器学习: library/book/Financial Signal Processing and Machine Learning/index.md
          - Hands-On AI for Banking: library/book/Hands-On Artificial Intelligence for Banking/index.md
          - 金融网络安全实战: library/book/Hands-On Cybersecurity for Finance/index.md
          - 算法交易机器学习实战: library/book/Hands-On Machine Learning for Algorithmic Trading/index.md
          - 算法交易机器学习: library/book/Machine-Learning-for-Algorithmic-Trading/index.md
          - 金融强化学习: library/book/Reinforcement Learning for Finance_ Solve Problems in Finance with CNN and RNN Using the TensorFlow Library/index.md
        - 前沿技术应用:
          - 主动投资组合管理新发展: library/book/前沿专题/Advances in Active Portfolio Management/index.md
          - 金融机器学习进阶: library/book/前沿专题/Advances in Financial Machine Learning/index.md
          - 算法交易方法: library/book/前沿专题/Algorithmic Trading Methods/index.md
          - 高级算法交易方法: library/book/前沿专题/Algorithmic Trading Methods_ Applications Using Advanced Statistics, Optimization, and Machine Learning Techniques-Academic Press (2020)/index.md
          - 算法与高频交易: library/book/前沿专题/Algorithmic and High-Frequency Trading/index.md
          - 算法与高频交易剑桥版: library/book/前沿专题/Algorithmic and High-Frequency Trading-Cambridge University Press (2015)/index.md
          - 计算智能应用: library/book/前沿专题/Applications of computational intelligence in data-driven trading-Wiley (2020)/index.md
          - 机器学习资产定价: library/book/前沿专题/Empirical Asset Pricing via Machine Learning/index.md
          - 金融科技案例: library/book/前沿专题/Financial Technology_ Case Studies in Fintech Innovation-Kogan Page (2020)/index.md
          - 资产管理机器学习: library/book/前沿专题/Machine Learning for Asset Managers/index.md
          - 资产管理机器学习剑桥版: library/book/前沿专题/Machine Learning for Asset Managers-Cambridge University Press (2020)/index.md
          - 资产定价机器学习: library/book/前沿专题/Machine Learning in Asset Pricing/index.md
          - 资产定价机器学习普林斯顿版: library/book/前沿专题/Machine Learning in Asset Pricing-Princeton Univ Pr (2021)/index.md
          - 金融机器学习实践: library/book/前沿专题/Machine Learning in Finance_ From Theory to Practice-Springer (2020)/index.md
          - 金融机器学习进阶: library/book/前沿专题/Marcos López de Prado - Advances in Financial Machine Learning-John Wiley & Sons, Inc. (2018)/index.md
          - 量子金融: library/book/前沿专题/Quantum Finance_ Path Integrals and Hamiltonians for Options and Interest Rates-Cambridge University Press (2004)/index.md
          - 系统化交易: library/book/前沿专题/Robert Carver - Systematic Trading_ A unique new method for designing trading and investing systems-Harriman House (2015)/index.md
          - 另类数据指南: library/book/前沿专题/The Book of Alternative Data_ A Guide for Investors Traders and Risk Managers-Wiley (2020)/index.md
          - 量子机器学习: library/book/Quantum Machine Learning and Optimisation in Finance_ On the Road to Quantum Advantage/index.md
          - 概率机器学习: library/book/Probabilistic Machine Learning for Finance and Investing/index.md

      - 量化交易:
        - 算法交易:
          - 算法与高频交易: library/book/algorithmic-and-high-frequency-trading-pdf-free/index.md
          - 高频交易系统开发: library/book/Developing High Frequency Trading Systems/index.md
          - Python金融交易实战: library/book/Hands-On Financial Trading with Python/index.md
          - 算法交易机器学习: library/book/Machine Learning for Algorithmic Trading/index.md
          - Python算法交易: library/book/Python for Algorithmic Trading_ From Idea to Cloud Deployment/index.md
        - 策略研究:
          - 101个因子公式: library/book/量化交易/101 Formulaic Alphas - arXiv.org/index.md
          - 151个交易策略: library/book/量化交易/151 Trading Strategies/index.md
          - 151交易策略论文版: library/book/量化交易/151 Trading Strategies论文版/index.md
          - 期货市场完全指南: library/book/量化交易/A Complete Guide to the Futures Market/index.md
          - 期货市场完全指南2: library/book/量化交易/A complete guide to the futures market_ technical analysis and trading systems, fundamental analysis, options, spreads, and trading principles-J/index.md
          - 主动投资组合管理: library/book/量化交易/Active Portfolio Management/index.md
          - 主动投资组合管理量化方法: library/book/量化交易/Active Portfolio Management_ A Quantitative Approach for Producing Superior Returns and Controlling Risk-McGraw-Hill (1999)/index.md
          - 算法交易: library/book/量化交易/Algorithmic Trading/index.md
          - 算法交易获胜策略: library/book/量化交易/Algorithmic Trading_ Winning Strategies and Their Rationale-Wiley (2013)/index.md
          - 行为金融: library/book/量化交易/BetaPlus_Behavioral_Finance/index.md
          - 盈利因子: library/book/量化交易/BetaPlus_Profitability_Factor/index.md
          - 时间序列分析: library/book/量化交易/BetaPlus_Time_Series_Analysis/index.md
          - 趋势跟踪: library/book/量化交易/BetaPlus_Trend_Following/index.md
          - Alpha挖掘: library/book/量化交易/Finding Alphas/index.md
          - 高频交易实践: library/book/量化交易/High-frequency trading/index.md
          - 高频交易实用指南: library/book/量化交易/High-frequency trading_ a practical guide to algorithmic strategies and trading systems-Wiley (2013)/index.md
          - 期权波动率与定价: library/book/Option Volatility and Pricing/index.md
          - 金融优化方法: library/book/量化交易/Optimization Methods in Finance/index.md
          - 量化股票投资组合管理: library/book/量化交易/Quantitative Equity Portfolio Management_ An Active Approach to Portfolio Construction and Management (McGraw-Hill Library of Investment and Finance)-McGraw-Hill Edu/index.md
          - 量化投资分析习题册: library/book/量化交易/Quantitative Investment Analysis Workbook, 3rd Edition-Wiley (2015)/index.md
          - 量化风险管理: library/book/量化交易/Quantitative Risk Management/index.md
          - 获取Alpha的量化策略: library/book/量化交易/Quantitative Strategies for Achieving Alpha (McGraw-Hill Finance & Investing)-McGraw-Hill Finance & Investing (2008)/index.md
          - 量化交易业务构建: library/book/量化交易/Quantitative Trading_ How to Build Your Own Algorithmic Trading Business-Wiley (2008)/index.md
          - 量化交易系统构建Wiley版: library/book/Quantitative Trading_ How to Build Your Own Algorithmic Trading Business (Wiley Trading) (2021, Wiley)/index.md
          - 统计套利算法交易: library/book/量化交易/Statistical Arbitrage/index.md
          - 波动率微笑: library/book/量化交易/The Volatility Smile/index.md
          - 波动率交易网站版: library/book/量化交易/Volatility Trading, + Website-Wiley (2013)/index.md
          - 理解资产价格: library/book/量化交易/理解资产价格-NobelEconomics1/index.md
          - 因子投资方法与实践: library/book/量化交易/石川_ 刘洋溢_ 连祥斌 - 因子投资：方法与实践-电子工业出版社 (2020)/index.md

      - 基础理论:
        - 数学与统计:
          - 金融数学技术导论: library/book/ Mathematical Techniques in Finance_ An Introduction (2022, Wiley)/index.md
          - 蒙特卡洛方法: library/book/Monte Carlo Methods in Financial Engineering/index.md
          - 金融优化方法: library/book/Optimization Methods in Finance（第二版）/index.md
          - 金融大数据建模: library/book/Stochastic Modelling of Big Data in Finance/index.md
        - 金融数学:
          - 金融工程线性代数: library/book/金融数学/A Linear Algebra Primer for Financial Engineering/index.md
          - 金融工程数学入门: library/book/金融数学/A Primer for the Mathematics of Financial Engineering/index.md
          - 随机模型与风险: library/book/金融数学/Advanced Stochastic Models, Risk Assessment, and Portfolio Optimization/index.md
          - 金融衍生品数学: library/book/金融数学/An Introduction to the Mathematics of Financial Derivatives/index.md
          - 金融衍生品数学导论: library/book/金融数学/An Introduction to the Mathematics of Financial Derivatives-Academic Press (2014)/index.md
          - 风险与资产配置: library/book/金融数学/Attilio Meucci - Risk and Asset Allocation/index.md
          - Dan Stefanica金融工程数学: library/book/金融数学/Dan Stefanica - A Primer for the Mathematics of Financial Engineering-FE Press (2008)/index.md
          - 动态资产定价: library/book/金融数学/Darrell Duffie - Dynamic asset pricing theory/index.md
          - 动态资产定价理论: library/book/金融数学/Darrell Duffie - Dynamic asset pricing theory-Princeton University Press (2001)/index.md
          - 动态资产定价理论: library/book/金融数学/Dynamic asset pricing theory/index.md
          - 实证资产定价: library/book/金融数学/Empirical Dynamic Asset Pricing/index.md
          - 实证动态资产定价: library/book/金融数学/Empirical Dynamic Asset Pricing_ Model Specification and Econometric Assessment (2006)/index.md
          - 市场微观结构: library/book/金融数学/Maureen O'Hara - Market Microstructure Theory  -Wiley/index.md
          - 市场微观结构理论: library/book/金融数学/Market Microstructure Theory/index.md
          - 金融数学方法: library/book/金融数学/Methods of Mathematical Finance/index.md
          - 数学金融方法: library/book/金融数学/Methods of Mathematical Finance-Springer (2001)/index.md
          - 量化金融导论: library/book/金融数学/Paul Wilmott - Paul Wilmott introduces quantitative finance-Wiley (2007)/index.md
          - Paul Wilmott量化金融导论: library/book/金融数学/Paul Wilmott introduces quantitative finance/index.md
          - 量化风险管理: library/book/金融数学/Quantitative risk management/index.md
          - 量化风险管理工具: library/book/金融数学/Quantitative risk management _ concepts, techniques and tools-Princeton University Press (2015)/index.md
          - 风险与资产配置: library/book/金融数学/Risk and Asset Allocation/index.md
          - 金融随机微积分: library/book/金融数学/Stochastic Calculus for Finance/index.md
          - 波动率微笑: library/book/金融数学/The Volatility Smile/index.md

      - 工程实现:
        - 编程实现:
          - Python金融理论: library/book/Financial Theory with Python/index.md
          - R量化金融: library/book/Learning Quantitative Finance with R/index.md
          - R语言量化金融学习: library/book/Learning-Quantitative-Finance-with-R/index.md
          - Python金融编程进阶: library/book/Mastering Python for Finance_ Implement advanced state-of-the-art financial statistical applications using Python/index.md
          - Pandas金融数据分析精通: library/book/Mastering pandas for Finance_ Master pandas, an open source Python Data Analysis Library, for financial data analysis/index.md
          - Python金融进阶: library/book/Python for Algorithmic Trading_ From Idea to Cloud Deployment/index.md
          - Python金融手册: library/book/Python for Finance Cookbook_ Over 80 powerful recipes for effective financial data analysis, 2nd Edition/index.md
        - 风险管理:
          - 风险管理机器学习: library/book/Machine Learning for Financial Risk Management with Python/index.md
          - 金融信号处理: library/book/Python for Finance Cookbook_ Over 80 powerful recipes for effective financial data analysis, 2nd Edition/index.md

      - 面试资源:
        - 量化面试指南:
          - 50个挑战性问题: library/book/fifty_challenging_problems_in__2(1)/index.md
          - 量化面试问题答案: library/book/quant job interview questions and answers/index.md
          - 量化金融常见问题: library/book/量化面试/Paul P. Wilmott - Frequently Asked Questions in Quantitative Finance, Second Edition  -A John Wiley and Sons, Ltd. (2009)/index.md
          - 量化金融FAQ: library/book/量化面试/Frequently Asked Questions in Quantitative Finance/index.md
          - Quant绿皮书精讲: library/book/量化面试/Quant绿皮书精讲60题_by野荷马/index.md
          - Quant绿皮书精讲60题: library/book/量化面试/Quant绿皮书精讲60题/index.md
          - 华尔街面试题: library/book/量化面试/Timothy Falcon Crack - Heard on the Street, Quantitative Questions from Wall Street Job Interviews-Timothy Crack (2014)/index.md
          - 街头听闻: library/book/量化面试/Heard on the Street/index.md
          - 量化面试红宝书: library/book/量化面试/红宝书Quant Job Interview Questions And Answers/index.md
          - 量化面试实践指南: library/book/量化面试/绿皮书A Practical Guide to Quantitative Finance Interviews/index.md
          - 150个高频题: library/book/量化面试/黄皮书150 Most Frequently Asked Questions on Quant Interviews/index.md
        - 经典面试书籍:
          - Heard街头量化问题: library/book/（I4-Heard）Heard On The Street Quantitative Questions From Wall Street Job Interviews/index.md
          - 黄皮书量化金融FAQ: library/book/（I5-黄皮书）Frequently Asked Questions In Quantitative Finance(1)/index.md
        - 中文精选:
          - 金融时间序列分析: library/book/【2022新书】机器学习在金融时间序列分析与预测中的应用，385页pdf/index.md
          - 金融数据科学: library/book/【开放书】经济与金融数据科学/index.md
          - 量化绿皮书: library/book/量化绿皮书/index.md
          - 量化必读: library/book/translated_The_Best_Books_for_Learning_Quantitative_Finance.md
        - 2024独家金融干货包: library/book/2024独家金融干货包/index.md
  - 行业内幕:
    - 简介: industry/overview.md
    - 公司简介:
      - 买方公司: industry/buy-side.md
      - 卖方公司: industry/sell-side.md
    - 大师人物:
      - 西蒙斯: industry/master/Jim-Simons.md
      - Giuseppe Paleologo: industry/Giuseppe-Paleologo.md
      - Julian Robertson: industry/Julian Robertson.md
    - 公司文化深度解析:
      - Citadel与Millennium文化对比: industry/Citadel-Millennium-culture.md
    - 基金管理策略:
      - 多管理人基金成功之道: industry/multi-manager-succ.md
    - 2025年最值得关注的10家对冲基金: industry/2025-10-fund.md
    - 大奖章基金：文艺复兴科技公司里独一无二的赚钱机器: industry/Medallion.md
    - Quadrature Capital:你从未听过的神秘自营交易公司: industry/Quadrature-Capital.md
    - 规模越大代表业绩越好？论对冲基金规模与其表现的关系: industry/size-fund.md
    - 量化行业与雇主类型全览: industry/translated_The_Various_Types_of_Quants_and_Quant_Employers.md 
    - 量化薪资揭秘：量化从业者赚多少钱？: industry/translated_Quantitative_Finance_Salaries_-_How_much_Money_do_Quants_Make.md
  - 求职专区:
    - 全球量化薪资大揭秘: job/quant-salary.md
    - 一文全解析对冲基金的职业路径: job/quant-job.md
    - 揭秘量化分析师的日常: job/quant-daily.md
    - 探秘Jane Street实习的亲身经历: job/Jane-Street-intern.md
    - 剑桥北大课程: job/cam-pku-course.md
    - 城市如何影响你的量化生涯: job/translated_The_Best_Cities_for_a_Career_in_Quantitative_Finance.md
    - 量化金融最佳学位推荐: job/translated_The_Best_Quantitative_Finance_Degrees.md
    - 量化开发者职业路径解析: job/translated_The_Quantitative_Developer_Career_Path.md
    - 量化交易员职业路径揭秘: job/translated_The_Quantitative_Trader_Career_Path.md
    - 两种量化面试官类型解析: job/translated_The_Two_Types_of_Quant_Interviewers.md
    - 量化交易员的日常工作揭秘: job/translated_What_does_a_Quantitative_Trader_do.md
    - 如何写出完美的量化简历: job/translated_Writing_the_Perfect_Quant_Resume.md
    - 2023量化金融求职与实习指南: job/translated_How_to_get_a_Quantitative_Finance_Job_or_Internship_in_2023.md
    - 如何拿下IMC Trading量化实习: job/translated_How_to_Land_a_Quant_Internship_at_IMC_Trading.md
    - 如何拿下Jane Street量化实习: job/translated_How_To_Land_a_Quant_Internship_at_Jane_Street.md
    - 如何拿下Optiver量化实习: job/translated_How_to_Land_a_Quant_Internship_at_Optiver.md
    - 如何进入Akuna Capital做量化交易: job/translated_How_to_Land_a_Quant_Trader_Job_at_Akuna_Capital.md
    - 量化交易员面试问题大全: job/translated_Interview_Questions_for_the_Quantitative_Trader_Job.md
  - 独家资源✨:
    - Overview: llmquant_resources/overview.md

# Configuration
theme:
  name: material
  logo: asset/quant-wiki.svg # logo 图标
  favicon: asset/quant-wiki.svg # 网页标签图标
  icon:
    repo: fontawesome/brands/github
  # font:
  #   text: LXGW WenKai Mono TC
  #   code: Roboto Mono
  language: zh
  features:
    - navigation.instant
    - navigation.tabs
    - navigation.sections
    - navigation.top
    - search.highlight
    - search.share
    - search.suggest
  palette:
    - scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    - scheme: slate
      primary: indigo
      accent: indigo
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
    

# Extensions
markdown_extensions:
  - admonition
  - codehilite:
      guess_lang: false
  - footnotes
  - meta
  - toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
  # https://facelessuser.github.io/pymdown-extensions/extensions/betterem/
  - pymdownx.betterem
  - pymdownx.details
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight
  - pymdownx.superfences
  - pymdownx.tasklist:
      custom_checkbox: true

# Plugins
plugins:
  - search
  - minify:
      minify_html: true
      minify_js: true
      minify_css: true
      htmlmin_opts:
        remove_comments: true
  # - git-revision-date-localized:
      # type: datetime

# Extra CSS and JavaScript
extra_css:
  - stylesheets/extra.css
  - https://cdn.jsdelivr.net/npm/lxgw-wenkai-webfont@1.1.0/style.css

extra_javascript:
  - javascripts/mathjax.js
  - https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js 


extra:
  analytics:  # Use Google Analytics to track the website traffic
    provider: google
    property: G-JSEDVW1BK1
  social:  # This is the social links section.
    # - icon: fontawesome/brands/github  # Use the Font Awesome icon name.
      # link: https://github.com/your-username  # Replace with your GitHub username.
      # name:  Visit me on GitHub # 用于 accessibility 的名字
    - icon: fontawesome/brands/linkedin
      link: https://www.linkedin.com/company/llmquant/
    - icon: fontawesome/solid/globe
      link: https://llmquant.com

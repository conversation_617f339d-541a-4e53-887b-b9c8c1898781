# Quant-Wiki 开源的中文量化百科

<img width="848" alt="image" src="https://github.com/user-attachments/assets/6d3b37b3-1bf3-4452-9833-f864c597c00d" />

## 👏欢迎访问 Quant Wiki 量化百科  [https://quant-wiki.com/](https://quant-wiki.com/)


> [!NOTE]
> 本项目正在开发中，欢迎大家参与贡献。

我们致力于量化知识的**开源**与**汉化**，打破国内外量化金融行业信息差。¶

We are committed to the open-sourcing and localization of quantitative knowledge, aiming to bridge the information gap between the domestic and international quantitative finance industries.

量化投资（Quantitative Investing，简称 Quant）是一种以数学模型、统计分析和算法为基础的投资方式，是现代金融领域的重要分支。

Quant Wiki 致力于打造一个免费开放、持续更新的 量化金融（quantitative finance） 知识分享平台。在这里，大家可以学习量化交易的核心知识、常用模型、算法设计，以及交易中的实战策略。我们为大家准备了丰富的资料，包括因子模型、事件驱动策略、执行成本优化等内容，帮助大家快速掌握量化投资领域的核心技能，迈向专业化道路。

## **Content**

![Quant-Wiki框架](https://fastly.jsdelivr.net/gh/bucketio/img12@main/2025/01/21/1737422354226-7661075b-8ae2-4716-9569-7c8ec95323ae.png)

## Contributing

我们非常欢迎大家为 quant-wiki 贡献内容！

如果您希望为 quant-wiki 做贡献，请参考 [CONTRIBUTING.md](.github/CONTRIBUTING.md) 中的内容。

## Local Deploy

首先拉取代码，然后进入项目目录。

```bash
git clone https://github.com/LLMQuant/quant-wiki.git
cd quant-wiki
```

推荐使用 `venv` 创建虚拟环境，并安装依赖。

```bash
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

本地预览只需要运行如下命令即可：

```bash
mkdocs serve
```

## LICENSE

**署名-非商业性使用-相同方式共享 4.0 国际 (CC BY-NC-SA 4.0)**

考虑到量化知识的独特性和保护贡献者的权益，我们使用的[许可协议](https://creativecommons.org/licenses/by-nc-sa/4.0/legalcode.zh-Hans)概要 (但不是替代) 。 [免责声明](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh#).

**您可以自由地：**

- **共享** — 在任何媒介以任何形式复制、发行本作品

- **演绎** — 修改、转换或以本作品为基础进行创作

只要你遵守许可协议条款，许可人就无法收回你的这些权利。

**惟须遵守下列条件：**

- **署名** — 您必须给出[适当的署名](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh#)，提供指向本许可协议的链接，同时[标明是否（对原始作品）作了修改](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh#)。您可以用任何合理的方式来署名，但是不得以任何方式暗示许可人为您或您的使用背书。
- **非商业性使用** — 您不得将本作品用于[商业目的](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh#)。
- **相同方式共享** — 如果您再混合、转换或者基于本作品进行创作，您必须基于[与原先许可协议相同的许可协议](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh#) 分发您贡献的作品。

- **没有附加限制** — 您不得适用法律术语或者 [技术措施](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh#) 从而限制其他人做许可协议允许的事情。

### 声明：

- 您不必因为公共领域的作品要素而遵守许可协议，或者您的使用被可适用的 [例外或限制](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh#)所允许。
- 不提供担保。许可协议可能不会给与您意图使用的所必须的所有许可。例如，其他权利比如[形象权、隐私权或人格权](https://creativecommons.org/licenses/by-nc-sa/4.0/deed.zh#)可能限制您如何使用作品。

## A Special Thanks

Quant Wiki 的建立深受 [OI Wiki](https://oi-wiki.org/) 的启发。在内容组织、编写范式以及网站架构等方面，我们参考了 OI Wiki 诸多宝贵的经验。OI Wiki 作为算法竞赛领域的中文知识库，其高质量的内容和开放协作的模式一直是我们学习的榜样。

Quant Wiki 致力于为量化交易 (Quant) 社区提供类似的高质量信息与学习资源。我们希望能够像 OI Wiki 一样，汇聚社区的力量，构建一个全面、准确、易懂的量化知识平台，助力每一位 Quant 学习者和从业者。

在此，我们向 OI Wiki 团队及所有贡献者表示衷心的感谢！

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=LLMQuant/quant-wiki&type=Date)](https://star-history.com/#LLMQuant/quant-wiki&Date)
